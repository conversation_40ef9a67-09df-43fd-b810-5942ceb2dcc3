{"uuid": "49c91a10-a823-11eb-9557-319832f382cc", "lastMigration": 32, "name": "Subscription Checkout", "endpointPrefix": "subs-checkout/v1/checkout", "latency": 0, "port": 3001, "routes": [{"uuid": "a31356c0-d2d6-11eb-a936-4b3842d7c4e9", "documentation": "", "method": "get", "endpoint": "payment-options", "responses": [{"uuid": "19e5ae90-f043-11eb-bc47-dfb7587cf92c", "body": "{\n  \"status\": 200,\n  \"message\": \"Payment Options\",\n  \"data\": {\n    \"available_modes\": [\n      {\n        \"type\": \"UPI\",\n        \"available_handles\": [\n          {\n            \"handle\": \"hdfcbank\",\n            \"application\": \"GPay\"\n          },\n          {\n            \"handle\": \"okaxisbank\",\n            \"application\": \"GPay\"\n          },\n          {\n            \"handle\": \"paytm\"\n          },\n          {\n            \"handle\": \"oksbi\"\n          }\n        ],\n        \"validate_UPI\": true,\n        \"isIntentSupported\": true,\n        \"isQrSupported\": true,\n        \"isCollectSupported\": true,\n        \"intentSupport\": [\n          {\n            \"operatingSystem\": \"ANDROID\",\n            \"applications\": [\n              \"GPAY\",\n              \"PAYTM\",\n              \"BHIM\",\n              \"AMAZONPAY\"\n            ]\n          },\n          {\n            \"operatingSystem\": \"IOS\",\n            \"applications\": [\n              \"PAYTM\"\n            ]\n          }\n        ],\n        \"qrSupport\": [\n          \"GPAY\",\n          \"PAYTM\",\n          \"BHIM\",\n          \"AMAZONPAY\"\n        ]\n      }\n    ]\n  }\n}", "latency": 0, "statusCode": 200, "label": "UPI Only", "headers": [{"key": "X-Request-ID", "value": "adace6a14b1000f6a5c3408d96dcb522"}, {"key": "Access-Control-Expose-Headers", "value": "X-Request-ID"}], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "a31356c1-d2d6-11eb-a936-4b3842d7c4e9", "body": "{\n  \"status\": 200,\n  \"message\": \"Payment Options\",\n  \"data\": {\n    \"available_modes\": [\n      {\n        \"type\": \"E_MANDATE\",\n        \"account_type\": [\n          \"SAVINGS\",\n          \"CURRENT\"\n        ],\n        \"frequent_banks\": [\n          {\n            \"bank_id\": \"SBIN\",\n            \"bank_name\": \"STATE BANK OF INDIA\",\n            \"bank_display_name\": \"\",\n            \"bank_logo\": \"https://cashfreelogo.cashfree.com/checkout/SBIN.png\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"BARB\",\n            \"bank_name\": \"BANK OF BARODA\",\n            \"bank_display_name\": \"\",\n            \"bank_logo\": \"https://cashfreelogo.cashfree.com/checkout/BARB.png\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"ICIC\",\n            \"bank_name\": \"ICICI BANK LTD\",\n            \"bank_display_name\": \"\",\n            \"bank_logo\": \"https://cashfreelogo.cashfree.com/checkout/ICIC.png\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"HDFC\",\n            \"bank_name\": \"HDFC BANK LTD\",\n            \"bank_display_name\": \"\",\n            \"bank_logo\": \"https://cashfreelogo.cashfree.com/checkout/HDFC.png\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          }\n        ],\n        \"all_banks\": [\n          {\n            \"bank_id\": \"AIRP\",\n            \"bank_name\": \"AIRTEL PAYMENTS BANK LTD\",\n            \"bank_display_name\": \"Airtel Payments Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"APGB\",\n            \"bank_name\": \"ANDHRA PRAGATHI GRAMEENA BANK\",\n            \"bank_display_name\": \"Andhra Pragathi Grameena Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"AUBL\",\n            \"bank_name\": \"AU SMALL FINANCE BANK\",\n            \"bank_display_name\": \"AU Small Finance Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"UTIB\",\n            \"bank_name\": \"AXIS BANK\",\n            \"bank_display_name\": \"Axis Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"BDBL\",\n            \"bank_name\": \"BANDHAN BANK LTD\",\n            \"bank_display_name\": \"Bandhan Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"BARB\",\n            \"bank_name\": \"BANK OF BARODA\",\n            \"bank_display_name\": \"Bank of Baroda\",\n            \"bank_logo\": \"https://cashfreelogo.cashfree.com/checkout/BARB.png\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"BKID\",\n            \"bank_name\": \"BANK OF INDIA\",\n            \"bank_display_name\": \"Bank of India\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"MAHB\",\n            \"bank_name\": \"BANK OF MAHARASHTRA\",\n            \"bank_display_name\": \"Bank of Maharashtra\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"CNRB\",\n            \"bank_name\": \"CANARA BANK\",\n            \"bank_display_name\": \"Canara Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"CLBL\",\n            \"bank_name\": \"CAPITAL SMALL FINANCE BANK LTD\",\n            \"bank_display_name\": \"Capital Small Finance Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"CSBK\",\n            \"bank_name\": \"THE CATHOLIC SYRIAN BANK\",\n            \"bank_display_name\": \"Catholic Syrian Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"CBIN\",\n            \"bank_name\": \"CENTRAL BANK OF INDIA\",\n            \"bank_display_name\": \"Central Bank of India\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"CITI\",\n            \"bank_name\": \"CITIBANK N A\",\n            \"bank_display_name\": \"Citibank India\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"CIUB\",\n            \"bank_name\": \"CITY UNION BANK LTD\",\n            \"bank_display_name\": \"City Union Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"COSB\",\n            \"bank_name\": \"THE COSMOS CO-OPERATIVE BANK LTD\",\n            \"bank_display_name\": \"Cosmos Co-op Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"DBSS\",\n            \"bank_name\": \"DBS BANK INDIA LTD\",\n            \"bank_display_name\": \"DBS Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"DEUT\",\n            \"bank_name\": \"DEUTSCHE BANK AG\",\n            \"bank_display_name\": \"Deutsche Bank Ag\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"DCBL\",\n            \"bank_name\": \"DCB BANK LTD\",\n            \"bank_display_name\": \"Development Credit Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"DLXB\",\n            \"bank_name\": \"DHANALAXMI BANK\",\n            \"bank_display_name\": \"Dhanlaxmi Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"ESFB\",\n            \"bank_name\": \"EQUITAS SMALL FINANCE BANK LTD\",\n            \"bank_display_name\": \"Equitas Small Finance Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"FDRL\",\n            \"bank_name\": \"FEDERAL BANK\",\n            \"bank_display_name\": \"Federal Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"HDFC\",\n            \"bank_name\": \"HDFC BANK LTD\",\n            \"bank_display_name\": \"HDFC Bank\",\n            \"bank_logo\": \"https://cashfreelogo.cashfree.com/checkout/HDFC.png\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"HSBC\",\n            \"bank_name\": \"THE HONGKONG AND SHANGHAI BANKING CORPORATION LTD\",\n            \"bank_display_name\": \"HSBC Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"ICIC\",\n            \"bank_name\": \"ICICI BANK LTD\",\n            \"bank_display_name\": \"ICICI Bank\",\n            \"bank_logo\": \"https://cashfreelogo.cashfree.com/checkout/ICIC.png\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"IBKL\",\n            \"bank_name\": \"IDBI BANK\",\n            \"bank_display_name\": \"IDBI Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"IDFB\",\n            \"bank_name\": \"IDFC FIRST BANK LTD\",\n            \"bank_display_name\": \"IDFC Bank Limited\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"IDIB\",\n            \"bank_name\": \"INDIAN BANK\",\n            \"bank_display_name\": \"Indian Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"IOBA\",\n            \"bank_name\": \"INDIAN OVERSEAS BANK\",\n            \"bank_display_name\": \"Indian Overseas Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"INDB\",\n            \"bank_name\": \"INDUSIND BANK\",\n            \"bank_display_name\": \"IndusInd Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"JSFB\",\n            \"bank_name\": \"JANA SMALL FINANCE BANK LTD\",\n            \"bank_display_name\": \"Jana Small Finance Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"KARB\",\n            \"bank_name\": \"KARNATAKA BANK LTD\",\n            \"bank_display_name\": \"Karnataka Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"KVGB\",\n            \"bank_name\": \"KARNATAKA VIKAS GRAMEENA BANK\",\n            \"bank_display_name\": \"Karnataka Vikas Grameena Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"KVBL\",\n            \"bank_name\": \"KARUR VYSA BANK\",\n            \"bank_display_name\": \"Karur Vysya Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"KKBK\",\n            \"bank_name\": \"KOTAK MAHINDRA BANK LTD\",\n            \"bank_display_name\": \"Kotak Mahindra Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"KNSB\",\n            \"bank_name\": \"KURLA NAGARIK SAHAKARI BANK LTD\",\n            \"bank_display_name\": \"Kurla Nagarik Sahakari Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"MHSX\",\n            \"bank_name\": \"MAHESH SAHAKARI BANK LTD PUNE\",\n            \"bank_display_name\": \"Mahesh Sahakari Bank Ltd, Pune\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"PYTM\",\n            \"bank_name\": \"PAYTM PAYMENTS BANK LTD\",\n            \"bank_display_name\": \"Paytm Payments Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"PSIB\",\n            \"bank_name\": \"PUNJAB AND SIND BANK\",\n            \"bank_display_name\": \"Punjab & Sind Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"PUNB\",\n            \"bank_name\": \"PUNJAB NATIONAL BANK\",\n            \"bank_display_name\": \"Punjab National Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"RATN\",\n            \"bank_name\": \"RBL BANK LIMITED\",\n            \"bank_display_name\": \"RBL Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"SHIX\",\n            \"bank_name\": \"SHIVALIK SMALL FINANCE BANK LTD\",\n            \"bank_display_name\": \"Shivalik Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"SIBL\",\n            \"bank_name\": \"THE SOUTH INDIAN BANK LIMITED\",\n            \"bank_display_name\": \"South Indian Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"SCBL\",\n            \"bank_name\": \"STANDARD CHARTERED BANK\",\n            \"bank_display_name\": \"Standard Chartered Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"SBIN\",\n            \"bank_name\": \"STATE BANK OF INDIA\",\n            \"bank_display_name\": \"State Bank of India\",\n            \"bank_logo\": \"https://cashfreelogo.cashfree.com/checkout/SBIN.png\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"TMBL\",\n            \"bank_name\": \"TAMILNAD MERCANTILE BANK LTD\",\n            \"bank_display_name\": \"Tamilnad Mercantile Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"ACUX\",\n            \"bank_name\": \"THE ADARSH CO OP URBAN BANK LTD\",\n            \"bank_display_name\": \"The Adarsh Co Op Urban Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"CNSX\",\n            \"bank_name\": \"THE CHEMBUR NAGARIK SAHAKARI BANK\",\n            \"bank_display_name\": \"The Chembur Nagarik Sahakari Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"NCBL\",\n            \"bank_name\": \"THE NATIONAL CO OP BANK LTD\",\n            \"bank_display_name\": \"The National Co Op Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"VARA\",\n            \"bank_name\": \"THE VARACHHA CO OP BANK LTD\",\n            \"bank_display_name\": \"The Varachha Co-Op Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"ZCBL\",\n            \"bank_name\": \"THE ZOROASTRIAN CO OP BANK LTD\",\n            \"bank_display_name\": \"The Zoroastrian Co-operative Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"USFB\",\n            \"bank_name\": \"UJJIVAN SMALL FINANCE BANK LTD\",\n            \"bank_display_name\": \"Ujjivan Small Finance Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"UBIN\",\n            \"bank_name\": \"UNION BANK OF INDIA\",\n            \"bank_display_name\": \"Union Bank of India\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"YESB\",\n            \"bank_name\": \"YES BANK\",\n            \"bank_display_name\": \"Yes Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          }\n        ]\n      },\n      {\n        \"type\": \"UPI\",\n        \"available_handles\": [\n          {\n            \"handle\": \"hdfcbank\",\n            \"application\": \"GPay\"\n          },\n          {\n            \"handle\": \"okaxisbank\",\n            \"application\": \"GPay\"\n          },\n          {\n            \"handle\": \"paytm\"\n          },\n          {\n            \"handle\": \"oksbi\"\n          }\n        ],\n        \"validate_UPI\": true,\n        \"isIntentSupported\": true,\n        \"isQrSupported\": true,\n        \"isCollectSupported\": true,\n        \"intentSupport\": [\n          {\n            \"operatingSystem\": \"ANDROID\",\n            \"applications\": [\n              \"GPAY\",\n              \"PAYTM\",\n              \"BHIM\",\n              \"AMAZONPAY\"\n            ]\n          },\n          {\n            \"operatingSystem\": \"IOS\",\n            \"applications\": [\n              \"PAYTM\"\n            ]\n          }\n        ],\n        \"qrSupport\": [\n          \"GPAY\",\n          \"PAYTM\",\n          \"BHIM\",\n          \"AMAZONPAY\"\n        ]\n      },\n      {\n        \"type\": \"CARD\",\n        \"allowed_card_types\": [\n          \"CREDIT_CARD\",\n          \"DEBIT_CARD\"\n        ]\n      }\n    ]\n  }\n}", "latency": 0, "statusCode": 200, "label": "All Enabled", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "8d032e30-eaa3-4a98-a0cd-08571da29d87", "body": "{\n  \"status\": 200,\n  \"message\": \"Payment Options\",\n  \"data\": {\n    \"available_modes\": [\n      {\n        \"type\": \"CARD\",\n        \"available_modes\": [\n          \"DEBIT\"\n        ]\n      }\n    ]\n  }\n}", "latency": 0, "statusCode": 200, "label": "Card Enabled", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "e1522ea0-0b96-4943-8ed1-063dddcc2cbb", "body": "{\n  \"status\": 200,\n  \"message\": \"Payment Options\",\n  \"data\": {\n    \"available_modes\": [\n      {\n        \"type\": \"E_MANDATE\",\n        \"account_type\": [\n          \"SAVINGS\",\n          \"CURRENT\"\n        ],\n        \"frequent_banks\": [\n          {\n            \"bank_id\": \"SBIN\",\n            \"bank_name\": \"STATE BANK OF INDIA\",\n            \"bank_display_name\": \"\",\n            \"bank_logo\": \"https://cashfreelogo.cashfree.com/checkout/SBIN.png\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"BARB\",\n            \"bank_name\": \"BANK OF BARODA\",\n            \"bank_display_name\": \"\",\n            \"bank_logo\": \"https://cashfreelogo.cashfree.com/checkout/BARB.png\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"ICIC\",\n            \"bank_name\": \"ICICI BANK LTD\",\n            \"bank_display_name\": \"\",\n            \"bank_logo\": \"https://cashfreelogo.cashfree.com/checkout/ICIC.png\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"HDFC\",\n            \"bank_name\": \"HDFC BANK LTD\",\n            \"bank_display_name\": \"\",\n            \"bank_logo\": \"https://cashfreelogo.cashfree.com/checkout/HDFC.png\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          }\n        ],\n        \"all_banks\": [\n          {\n            \"bank_id\": \"AIRP\",\n            \"bank_name\": \"AIRTEL PAYMENTS BANK LTD\",\n            \"bank_display_name\": \"Airtel Payments Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"APGB\",\n            \"bank_name\": \"ANDHRA PRAGATHI GRAMEENA BANK\",\n            \"bank_display_name\": \"Andhra Pragathi Grameena Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"AUBL\",\n            \"bank_name\": \"AU SMALL FINANCE BANK\",\n            \"bank_display_name\": \"AU Small Finance Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"UTIB\",\n            \"bank_name\": \"AXIS BANK\",\n            \"bank_display_name\": \"Axis Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"BDBL\",\n            \"bank_name\": \"BANDHAN BANK LTD\",\n            \"bank_display_name\": \"Bandhan Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"BARB\",\n            \"bank_name\": \"BANK OF BARODA\",\n            \"bank_display_name\": \"Bank of Baroda\",\n            \"bank_logo\": \"https://cashfreelogo.cashfree.com/checkout/bank_of_baroda.png\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"BKID\",\n            \"bank_name\": \"BANK OF INDIA\",\n            \"bank_display_name\": \"Bank of India\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"MAHB\",\n            \"bank_name\": \"BANK OF MAHARASHTRA\",\n            \"bank_display_name\": \"Bank of Maharashtra\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"CNRB\",\n            \"bank_name\": \"CANARA BANK\",\n            \"bank_display_name\": \"Canara Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"CLBL\",\n            \"bank_name\": \"CAPITAL SMALL FINANCE BANK LTD\",\n            \"bank_display_name\": \"Capital Small Finance Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"CSBK\",\n            \"bank_name\": \"THE CATHOLIC SYRIAN BANK\",\n            \"bank_display_name\": \"Catholic Syrian Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"CBIN\",\n            \"bank_name\": \"CENTRAL BANK OF INDIA\",\n            \"bank_display_name\": \"Central Bank of India\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"CITI\",\n            \"bank_name\": \"CITIBANK N A\",\n            \"bank_display_name\": \"Citibank India\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"CIUB\",\n            \"bank_name\": \"CITY UNION BANK LTD\",\n            \"bank_display_name\": \"City Union Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"COSB\",\n            \"bank_name\": \"THE COSMOS CO-OPERATIVE BANK LTD\",\n            \"bank_display_name\": \"Cosmos Co-op Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"DBSS\",\n            \"bank_name\": \"DBS BANK INDIA LTD\",\n            \"bank_display_name\": \"DBS Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"DEUT\",\n            \"bank_name\": \"DEUTSCHE BANK AG\",\n            \"bank_display_name\": \"Deutsche Bank Ag\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"DCBL\",\n            \"bank_name\": \"DCB BANK LTD\",\n            \"bank_display_name\": \"Development Credit Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"DLXB\",\n            \"bank_name\": \"DHANALAXMI BANK\",\n            \"bank_display_name\": \"Dhanlaxmi Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"ESFB\",\n            \"bank_name\": \"EQUITAS SMALL FINANCE BANK LTD\",\n            \"bank_display_name\": \"Equitas Small Finance Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"FDRL\",\n            \"bank_name\": \"FEDERAL BANK\",\n            \"bank_display_name\": \"Federal Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"HDFC\",\n            \"bank_name\": \"HDFC BANK LTD\",\n            \"bank_display_name\": \"HDFC Bank\",\n            \"bank_logo\": \"https://cashfreelogo.cashfree.com/checkout/hdfc.png\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"HSBC\",\n            \"bank_name\": \"THE HONGKONG AND SHANGHAI BANKING CORPORATION LTD\",\n            \"bank_display_name\": \"HSBC Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"ICIC\",\n            \"bank_name\": \"ICICI BANK LTD\",\n            \"bank_display_name\": \"ICICI Bank\",\n            \"bank_logo\": \"https://cashfreelogo.cashfree.com/checkout/icici.png\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"IBKL\",\n            \"bank_name\": \"IDBI BANK\",\n            \"bank_display_name\": \"IDBI Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"IDFB\",\n            \"bank_name\": \"IDFC FIRST BANK LTD\",\n            \"bank_display_name\": \"IDFC Bank Limited\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"IDIB\",\n            \"bank_name\": \"INDIAN BANK\",\n            \"bank_display_name\": \"Indian Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"IOBA\",\n            \"bank_name\": \"INDIAN OVERSEAS BANK\",\n            \"bank_display_name\": \"Indian Overseas Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"INDB\",\n            \"bank_name\": \"INDUSIND BANK\",\n            \"bank_display_name\": \"IndusInd Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"JSFB\",\n            \"bank_name\": \"JANA SMALL FINANCE BANK LTD\",\n            \"bank_display_name\": \"Jana Small Finance Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"KARB\",\n            \"bank_name\": \"KARNATAKA BANK LTD\",\n            \"bank_display_name\": \"Karnataka Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"KVGB\",\n            \"bank_name\": \"KARNATAKA VIKAS GRAMEENA BANK\",\n            \"bank_display_name\": \"Karnataka Vikas Grameena Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"KVBL\",\n            \"bank_name\": \"KARUR VYSA BANK\",\n            \"bank_display_name\": \"Karur Vysya Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"KKBK\",\n            \"bank_name\": \"KOTAK MAHINDRA BANK LTD\",\n            \"bank_display_name\": \"Kotak Mahindra Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"KNSB\",\n            \"bank_name\": \"KURLA NAGARIK SAHAKARI BANK LTD\",\n            \"bank_display_name\": \"Kurla Nagarik Sahakari Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"MHSX\",\n            \"bank_name\": \"MAHESH SAHAKARI BANK LTD PUNE\",\n            \"bank_display_name\": \"Mahesh Sahakari Bank Ltd, Pune\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"PYTM\",\n            \"bank_name\": \"PAYTM PAYMENTS BANK LTD\",\n            \"bank_display_name\": \"Paytm Payments Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"PSIB\",\n            \"bank_name\": \"PUNJAB AND SIND BANK\",\n            \"bank_display_name\": \"Punjab & Sind Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"PUNB\",\n            \"bank_name\": \"PUNJAB NATIONAL BANK\",\n            \"bank_display_name\": \"Punjab National Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"RATN\",\n            \"bank_name\": \"RBL BANK LIMITED\",\n            \"bank_display_name\": \"RBL Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"SHIX\",\n            \"bank_name\": \"SHIVALIK SMALL FINANCE BANK LTD\",\n            \"bank_display_name\": \"Shivalik Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"SIBL\",\n            \"bank_name\": \"THE SOUTH INDIAN BANK LIMITED\",\n            \"bank_display_name\": \"South Indian Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"SCBL\",\n            \"bank_name\": \"STANDARD CHARTERED BANK\",\n            \"bank_display_name\": \"Standard Chartered Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"SBIN\",\n            \"bank_name\": \"STATE BANK OF INDIA\",\n            \"bank_display_name\": \"State Bank of India\",\n            \"bank_logo\": \"https://cashfreelogo.cashfree.com/checkout/sbi.png\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"TMBL\",\n            \"bank_name\": \"TAMILNAD MERCANTILE BANK LTD\",\n            \"bank_display_name\": \"Tamilnad Mercantile Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"CNSX\",\n            \"bank_name\": \"THE CHEMBUR NAGARIK SAHAKARI BANK\",\n            \"bank_display_name\": \"The Chembur Nagarik Sahakari Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"NCBL\",\n            \"bank_name\": \"THE NATIONAL CO OP BANK LTD\",\n            \"bank_display_name\": \"The National Co Op Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"VARA\",\n            \"bank_name\": \"THE VARACHHA CO OP BANK LTD\",\n            \"bank_display_name\": \"The Varachha Co-Op Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"ZCBL\",\n            \"bank_name\": \"THE ZOROASTRIAN CO OP BANK LTD\",\n            \"bank_display_name\": \"The Zoroastrian Co-operative Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"USFB\",\n            \"bank_name\": \"UJJIVAN SMALL FINANCE BANK LTD\",\n            \"bank_display_name\": \"Ujjivan Small Finance Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"UBIN\",\n            \"bank_name\": \"UNION BANK OF INDIA\",\n            \"bank_display_name\": \"Union Bank of India\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"YESB\",\n            \"bank_name\": \"YES BANK\",\n            \"bank_display_name\": \"Yes Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          }\n        ]\n      }\n    ]\n  }\n}", "latency": 0, "statusCode": 200, "label": "Emandate Enabled", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "a377c5be-fd02-44bb-a99d-8b38ed6dff20", "body": "{\n  \"status\": 200,\n  \"message\": \"Payment Options\",\n  \"data\": {\n    \"available_modes\": []\n  }\n}", "latency": 0, "statusCode": 200, "label": "All disabled", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "d09b086a-d8f2-45a4-a246-4abe41a66bc8", "body": "{\n  \"status\": 200,\n  \"message\": \"Payment Options\",\n  \"data\": {\n    \"available_modes\": [\n      {\n        \"type\": \"E_MANDATE\",\n        \"account_type\": [\n          \"SAVINGS\",\n          \"CURRENT\"\n        ],\n        \"frequent_banks\": [\n          {\n            \"bank_id\": \"SBIN\",\n            \"bank_name\": \"STATE BANK OF INDIA\",\n            \"bank_display_name\": \"\",\n            \"bank_logo\": \"https://cashfreelogo.cashfree.com/checkout/SBIN.png\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"BARB\",\n            \"bank_name\": \"BANK OF BARODA\",\n            \"bank_display_name\": \"Bank of Baroda\",\n            \"bank_logo\": \"https://cashfreelogo.cashfree.com/checkout/BARB.png\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"ICIC\",\n            \"bank_name\": \"ICICI BANK LTD\",\n            \"bank_display_name\": \"\",\n            \"bank_logo\": \"https://cashfreelogo.cashfree.com/checkout/ICIC.png\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"HDFC\",\n            \"bank_name\": \"HDFC BANK LTD\",\n            \"bank_display_name\": \"\",\n            \"bank_logo\": \"https://cashfreelogo.cashfree.com/checkout/HDFC.png\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          }\n        ],\n        \"all_banks\": [\n          {\n            \"bank_id\": \"AIRP\",\n            \"bank_name\": \"AIRTEL PAYMENTS BANK LTD\",\n            \"bank_display_name\": \"Airtel Payments Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"APGB\",\n            \"bank_name\": \"ANDHRA PRAGATHI GRAMEENA BANK\",\n            \"bank_display_name\": \"Andhra Pragathi Grameena Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"AUBL\",\n            \"bank_name\": \"AU SMALL FINANCE BANK\",\n            \"bank_display_name\": \"AU Small Finance Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"UTIB\",\n            \"bank_name\": \"AXIS BANK\",\n            \"bank_display_name\": \"Axis Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"BDBL\",\n            \"bank_name\": \"BANDHAN BANK LTD\",\n            \"bank_display_name\": \"Bandhan Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"BARB\",\n            \"bank_name\": \"BANK OF BARODA\",\n            \"bank_display_name\": \"Bank of Baroda\",\n            \"bank_logo\": \"https://cashfreelogo.cashfree.com/checkout/BARB.png\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"BKID\",\n            \"bank_name\": \"BANK OF INDIA\",\n            \"bank_display_name\": \"Bank of India\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"MAHB\",\n            \"bank_name\": \"BANK OF MAHARASHTRA\",\n            \"bank_display_name\": \"Bank of Maharashtra\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"CNRB\",\n            \"bank_name\": \"CANARA BANK\",\n            \"bank_display_name\": \"Canara Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"CLBL\",\n            \"bank_name\": \"CAPITAL SMALL FINANCE BANK LTD\",\n            \"bank_display_name\": \"Capital Small Finance Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"CSBK\",\n            \"bank_name\": \"THE CATHOLIC SYRIAN BANK\",\n            \"bank_display_name\": \"Catholic Syrian Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"CBIN\",\n            \"bank_name\": \"CENTRAL BANK OF INDIA\",\n            \"bank_display_name\": \"Central Bank of India\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"CITI\",\n            \"bank_name\": \"CITIBANK N A\",\n            \"bank_display_name\": \"Citibank India\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"CIUB\",\n            \"bank_name\": \"CITY UNION BANK LTD\",\n            \"bank_display_name\": \"City Union Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"COSB\",\n            \"bank_name\": \"THE COSMOS CO-OPERATIVE BANK LTD\",\n            \"bank_display_name\": \"Cosmos Co-op Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"DBSS\",\n            \"bank_name\": \"DBS BANK INDIA LTD\",\n            \"bank_display_name\": \"DBS Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"DEUT\",\n            \"bank_name\": \"DEUTSCHE BANK AG\",\n            \"bank_display_name\": \"Deutsche Bank Ag\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"DCBL\",\n            \"bank_name\": \"DCB BANK LTD\",\n            \"bank_display_name\": \"Development Credit Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"DLXB\",\n            \"bank_name\": \"DHANALAXMI BANK\",\n            \"bank_display_name\": \"Dhanlaxmi Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"ESFB\",\n            \"bank_name\": \"EQUITAS SMALL FINANCE BANK LTD\",\n            \"bank_display_name\": \"Equitas Small Finance Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"FDRL\",\n            \"bank_name\": \"FEDERAL BANK\",\n            \"bank_display_name\": \"Federal Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"HDFC\",\n            \"bank_name\": \"HDFC BANK LTD\",\n            \"bank_display_name\": \"HDFC Bank\",\n            \"bank_logo\": \"https://cashfreelogo.cashfree.com/checkout/HDFC.png\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"HSBC\",\n            \"bank_name\": \"THE HONGKONG AND SHANGHAI BANKING CORPORATION LTD\",\n            \"bank_display_name\": \"HSBC Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"ICIC\",\n            \"bank_name\": \"ICICI BANK LTD\",\n            \"bank_display_name\": \"ICICI Bank\",\n            \"bank_logo\": \"https://cashfreelogo.cashfree.com/checkout/ICIC.png\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"IBKL\",\n            \"bank_name\": \"IDBI BANK\",\n            \"bank_display_name\": \"IDBI Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"IDFB\",\n            \"bank_name\": \"IDFC FIRST BANK LTD\",\n            \"bank_display_name\": \"IDFC Bank Limited\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"IDIB\",\n            \"bank_name\": \"INDIAN BANK\",\n            \"bank_display_name\": \"Indian Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"IOBA\",\n            \"bank_name\": \"INDIAN OVERSEAS BANK\",\n            \"bank_display_name\": \"Indian Overseas Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"INDB\",\n            \"bank_name\": \"INDUSIND BANK\",\n            \"bank_display_name\": \"IndusInd Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"JSFB\",\n            \"bank_name\": \"JANA SMALL FINANCE BANK LTD\",\n            \"bank_display_name\": \"Jana Small Finance Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"KARB\",\n            \"bank_name\": \"KARNATAKA BANK LTD\",\n            \"bank_display_name\": \"Karnataka Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"KVGB\",\n            \"bank_name\": \"KARNATAKA VIKAS GRAMEENA BANK\",\n            \"bank_display_name\": \"Karnataka Vikas Grameena Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"KVBL\",\n            \"bank_name\": \"KARUR VYSA BANK\",\n            \"bank_display_name\": \"Karur Vysya Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"KKBK\",\n            \"bank_name\": \"KOTAK MAHINDRA BANK LTD\",\n            \"bank_display_name\": \"Kotak Mahindra Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"KNSB\",\n            \"bank_name\": \"KURLA NAGARIK SAHAKARI BANK LTD\",\n            \"bank_display_name\": \"Kurla Nagarik Sahakari Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"MHSX\",\n            \"bank_name\": \"MAHESH SAHAKARI BANK LTD PUNE\",\n            \"bank_display_name\": \"Mahesh Sahakari Bank Ltd, Pune\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"PYTM\",\n            \"bank_name\": \"PAYTM PAYMENTS BANK LTD\",\n            \"bank_display_name\": \"Paytm Payments Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"PSIB\",\n            \"bank_name\": \"PUNJAB AND SIND BANK\",\n            \"bank_display_name\": \"Punjab & Sind Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"PUNB\",\n            \"bank_name\": \"PUNJAB NATIONAL BANK\",\n            \"bank_display_name\": \"Punjab National Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"RATN\",\n            \"bank_name\": \"RBL BANK LIMITED\",\n            \"bank_display_name\": \"RBL Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"SHIX\",\n            \"bank_name\": \"SHIVALIK SMALL FINANCE BANK LTD\",\n            \"bank_display_name\": \"Shivalik Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"SIBL\",\n            \"bank_name\": \"THE SOUTH INDIAN BANK LIMITED\",\n            \"bank_display_name\": \"South Indian Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"SCBL\",\n            \"bank_name\": \"STANDARD CHARTERED BANK\",\n            \"bank_display_name\": \"Standard Chartered Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"SBIN\",\n            \"bank_name\": \"STATE BANK OF INDIA\",\n            \"bank_display_name\": \"State Bank of India\",\n            \"bank_logo\": \"https://cashfreelogo.cashfree.com/checkout/SBIN.png\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"TMBL\",\n            \"bank_name\": \"TAMILNAD MERCANTILE BANK LTD\",\n            \"bank_display_name\": \"Tamilnad Mercantile Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"ACUX\",\n            \"bank_name\": \"THE ADARSH CO OP URBAN BANK LTD\",\n            \"bank_display_name\": \"The Adarsh Co Op Urban Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"CNSX\",\n            \"bank_name\": \"THE CHEMBUR NAGARIK SAHAKARI BANK\",\n            \"bank_display_name\": \"The Chembur Nagarik Sahakari Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"NCBL\",\n            \"bank_name\": \"THE NATIONAL CO OP BANK LTD\",\n            \"bank_display_name\": \"The National Co Op Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"VARA\",\n            \"bank_name\": \"THE VARACHHA CO OP BANK LTD\",\n            \"bank_display_name\": \"The Varachha Co-Op Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"ZCBL\",\n            \"bank_name\": \"THE ZOROASTRIAN CO OP BANK LTD\",\n            \"bank_display_name\": \"The Zoroastrian Co-operative Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"USFB\",\n            \"bank_name\": \"UJJIVAN SMALL FINANCE BANK LTD\",\n            \"bank_display_name\": \"Ujjivan Small Finance Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"UBIN\",\n            \"bank_name\": \"UNION BANK OF INDIA\",\n            \"bank_display_name\": \"Union Bank of India\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"YESB\",\n            \"bank_name\": \"YES BANK\",\n            \"bank_display_name\": \"Yes Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          }\n        ]\n      },\n      {\n        \"type\": \"UPI\",\n        \"validate_UPI\": false,\n        \"available_handles\": [\n          {\n            \"handle\": \"apl\",\n            \"application\": \"Amazon Pay\"\n          },\n          {\n            \"handle\": \"axl\",\n            \"application\": \"PhonePe\"\n          },\n          {\n            \"handle\": \"boi\",\n            \"application\": \"BHIM BOI UPI\"\n          },\n          {\n            \"handle\": \"cnrb\",\n            \"application\": \"BHIM Canara\"\n          },\n          {\n            \"handle\": \"gocash\",\n            \"application\": \"BHIM\"\n          },\n          {\n            \"handle\": \"ibl\",\n            \"application\": \"PhonePe\"\n          },\n          {\n            \"handle\": \"indus\",\n            \"application\": \"BHIM Indus Pay\"\n          },\n          {\n            \"handle\": \"okahdfcbank\",\n            \"application\": \"Google Pay\"\n          },\n          {\n            \"handle\": \"okaxis\",\n            \"application\": \"Google Pay\"\n          },\n          {\n            \"handle\": \"paytm\",\n            \"application\": \"Paytm\"\n          },\n          {\n            \"handle\": \"upi\",\n            \"application\": \"BHIM\"\n          },\n          {\n            \"handle\": \"vtest\",\n            \"application\": \"Jupiter Money\"\n          },\n          {\n            \"handle\": \"ybl\",\n            \"application\": \"PhonePe\"\n          }\n        ]\n      },\n      {\n        \"type\": \"CARD\",\n        \"allowed_card_types\": [\n          \"CREDIT_CARD\",\n          \"DEBIT_CARD\"\n        ]\n      }\n    ]\n  }\n}", "latency": 0, "statusCode": 200, "label": "Emandate UPI Enabled", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "f120ce28-332f-423a-881f-d8fbd970e75f", "body": "{\n  \"status\": 200,\n  \"message\": \"Payment Options\",\n  \"data\": {\n    \"available_modes\": [\n      {\n        \"type\": \"UPI\",\n        \"available_handles\": [\n          {\n            \"handle\": \"hdfcbank\",\n            \"application\": \"GPay\"\n          },\n          {\n            \"handle\": \"okaxisbank\",\n            \"application\": \"GPay\"\n          },\n          {\n            \"handle\": \"paytm\"\n          },\n          {\n            \"handle\": \"oksbi\"\n          }\n        ],\n        \"validate_UPI\": true,\n        \"isIntentSupported\": false,\n        \"isQrSupported\": true,\n        \"isCollectSupported\": true,\n        \"intentSupport\": [\n          {\n            \"operatingSystem\": \"ANDROID\",\n            \"applications\": [\n              \"GPAY\",\n              \"PAYTM\",\n              \"BHIM\",\n              \"AMAZONPAY\"\n            ]\n          },\n          {\n            \"operatingSystem\": \"IOS\",\n            \"applications\": [\n              \"PAYTM\"\n            ]\n          }\n        ],\n        \"qrSupport\": [\n          \"GPAY\",\n          \"PAYTM\",\n          \"BHIM\",\n          \"AMAZONPAY\"\n        ]\n      }\n    ]\n  }\n}", "latency": 0, "statusCode": 200, "label": "UPI Intent Disabled", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "crudKey": "id", "callbacks": []}, {"uuid": "1fc8acc4-4d7c-470b-b227-25745c9ff3c0", "body": "{\n  \"status\": 200,\n  \"message\": \"Payment Options\",\n  \"data\": {\n    \"available_modes\": [\n      {\n        \"type\": \"UPI\",\n        \"available_handles\": [\n          {\n            \"handle\": \"hdfcbank\",\n            \"application\": \"GPay\"\n          },\n          {\n            \"handle\": \"okaxisbank\",\n            \"application\": \"GPay\"\n          },\n          {\n            \"handle\": \"paytm\"\n          },\n          {\n            \"handle\": \"oksbi\"\n          }\n        ],\n        \"validate_UPI\": true,\n        \"isIntentSupported\": true,\n        \"isQrSupported\": false,\n        \"isCollectSupported\": true,\n        \"intentSupport\": [\n          {\n            \"operatingSystem\": \"ANDROID\",\n            \"applications\": [\n              \"GPAY\",\n              \"PAYTM\",\n              \"BHIM\",\n              \"AMAZONPAY\"\n            ]\n          },\n          {\n            \"operatingSystem\": \"IOS\",\n            \"applications\": [\n              \"PAYTM\"\n            ]\n          }\n        ],\n        \"qrSupport\": [\n          \"GPAY\",\n          \"PAYTM\",\n          \"BHIM\",\n          \"AMAZONPAY\"\n        ]\n      }\n    ]\n  }\n}", "latency": 0, "statusCode": 200, "label": "UPI QR Disabled", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "crudKey": "id", "callbacks": []}, {"uuid": "09aa533e-8f1e-4d3a-81c8-871ee845f818", "body": "{\n  \"status\": 200,\n  \"message\": \"Payment Options\",\n  \"data\": {\n    \"available_modes\": [\n      {\n        \"type\": \"E_MANDATE\",\n        \"account_type\": [\n          \"SAVINGS\",\n          \"CURRENT\"\n        ],\n        \"frequent_banks\": [\n          {\n            \"bank_id\": \"SBIN\",\n            \"bank_name\": \"STATE BANK OF INDIA\",\n            \"bank_display_name\": \"\",\n            \"bank_logo\": \"https://cashfreelogo.cashfree.com/checkout/SBIN.png\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"BARB\",\n            \"bank_name\": \"BANK OF BARODA\",\n            \"bank_display_name\": \"\",\n            \"bank_logo\": \"https://cashfreelogo.cashfree.com/checkout/BARB.png\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"ICIC\",\n            \"bank_name\": \"ICICI BANK LTD\",\n            \"bank_display_name\": \"\",\n            \"bank_logo\": \"https://cashfreelogo.cashfree.com/checkout/ICIC.png\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"HDFC\",\n            \"bank_name\": \"HDFC BANK LTD\",\n            \"bank_display_name\": \"\",\n            \"bank_logo\": \"https://cashfreelogo.cashfree.com/checkout/HDFC.png\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          }\n        ],\n        \"all_banks\": [\n          {\n            \"bank_id\": \"AIRP\",\n            \"bank_name\": \"AIRTEL PAYMENTS BANK LTD\",\n            \"bank_display_name\": \"Airtel Payments Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"APGB\",\n            \"bank_name\": \"ANDHRA PRAGATHI GRAMEENA BANK\",\n            \"bank_display_name\": \"Andhra Pragathi Grameena Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"AUBL\",\n            \"bank_name\": \"AU SMALL FINANCE BANK\",\n            \"bank_display_name\": \"AU Small Finance Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"UTIB\",\n            \"bank_name\": \"AXIS BANK\",\n            \"bank_display_name\": \"Axis Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"BDBL\",\n            \"bank_name\": \"BANDHAN BANK LTD\",\n            \"bank_display_name\": \"Bandhan Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"BARB\",\n            \"bank_name\": \"BANK OF BARODA\",\n            \"bank_display_name\": \"Bank of Baroda\",\n            \"bank_logo\": \"https://cashfreelogo.cashfree.com/checkout/BARB.png\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"BKID\",\n            \"bank_name\": \"BANK OF INDIA\",\n            \"bank_display_name\": \"Bank of India\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"MAHB\",\n            \"bank_name\": \"BANK OF MAHARASHTRA\",\n            \"bank_display_name\": \"Bank of Maharashtra\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"CNRB\",\n            \"bank_name\": \"CANARA BANK\",\n            \"bank_display_name\": \"Canara Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"CLBL\",\n            \"bank_name\": \"CAPITAL SMALL FINANCE BANK LTD\",\n            \"bank_display_name\": \"Capital Small Finance Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"CSBK\",\n            \"bank_name\": \"CSB Bank Limited\",\n            \"bank_display_name\": \"Catholic Syrian Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"CBIN\",\n            \"bank_name\": \"CENTRAL BANK OF INDIA\",\n            \"bank_display_name\": \"Central Bank of India\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"CITI\",\n            \"bank_name\": \"CITIBANK N A\",\n            \"bank_display_name\": \"Citibank India\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"CIUB\",\n            \"bank_name\": \"CITY UNION BANK LTD\",\n            \"bank_display_name\": \"City Union Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"COSB\",\n            \"bank_name\": \"THE COSMOS CO-OPERATIVE BANK LTD\",\n            \"bank_display_name\": \"Cosmos Co-op Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"DBSS\",\n            \"bank_name\": \"DBS BANK INDIA LTD\",\n            \"bank_display_name\": \"DBS Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"DEUT\",\n            \"bank_name\": \"DEUTSCHE BANK AG\",\n            \"bank_display_name\": \"Deutsche Bank Ag\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"DCBL\",\n            \"bank_name\": \"DCB BANK LTD\",\n            \"bank_display_name\": \"Development Credit Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"DLXB\",\n            \"bank_name\": \"DHANALAXMI BANK\",\n            \"bank_display_name\": \"Dhanlaxmi Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"ESFB\",\n            \"bank_name\": \"EQUITAS SMALL FINANCE BANK LTD\",\n            \"bank_display_name\": \"Equitas Small Finance Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"FDRL\",\n            \"bank_name\": \"FEDERAL BANK\",\n            \"bank_display_name\": \"Federal Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"HDFC\",\n            \"bank_name\": \"HDFC BANK LTD\",\n            \"bank_display_name\": \"HDFC Bank\",\n            \"bank_logo\": \"https://cashfreelogo.cashfree.com/checkout/HDFC.png\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"HSBC\",\n            \"bank_name\": \"THE HONGKONG AND SHANGHAI BANKING CORPORATION LTD\",\n            \"bank_display_name\": \"HSBC Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"ICIC\",\n            \"bank_name\": \"ICICI BANK LTD\",\n            \"bank_display_name\": \"ICICI Bank\",\n            \"bank_logo\": \"https://cashfreelogo.cashfree.com/checkout/ICIC.png\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"IBKL\",\n            \"bank_name\": \"IDBI BANK\",\n            \"bank_display_name\": \"IDBI Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"IDFB\",\n            \"bank_name\": \"IDFC FIRST BANK LTD\",\n            \"bank_display_name\": \"IDFC Bank Limited\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"IDIB\",\n            \"bank_name\": \"INDIAN BANK\",\n            \"bank_display_name\": \"Indian Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"INDB\",\n            \"bank_name\": \"INDUSIND BANK\",\n            \"bank_display_name\": \"IndusInd Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"JAKA\",\n            \"bank_name\": \"THE JAMMU AND KASHMIR BANK LTD\",\n            \"bank_display_name\": \"Jammu & Kashmir Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"JSFB\",\n            \"bank_name\": \"JANA SMALL FINANCE BANK LTD\",\n            \"bank_display_name\": \"Jana Small Finance Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"KCCB\",\n            \"bank_name\": \"THE KALUPUR COMMERCIAL CO OP BANK\",\n            \"bank_display_name\": \"Kalupur Commercial Co-op Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"KARB\",\n            \"bank_name\": \"KARNATAKA BANK LTD\",\n            \"bank_display_name\": \"Karnataka Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"KVGB\",\n            \"bank_name\": \"KARNATAKA VIKAS GRAMEENA BANK\",\n            \"bank_display_name\": \"Karnataka Vikas Grameena Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"KVBL\",\n            \"bank_name\": \"KARUR VYSA BANK\",\n            \"bank_display_name\": \"Karur Vysya Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"KKBK\",\n            \"bank_name\": \"KOTAK MAHINDRA BANK LTD\",\n            \"bank_display_name\": \"Kotak Mahindra Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"KNSB\",\n            \"bank_name\": \"KURLA NAGARIK SAHAKARI BANK LTD\",\n            \"bank_display_name\": \"Kurla Nagarik Sahakari Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"MHSX\",\n            \"bank_name\": \"MAHESH SAHAKARI BANK LTD PUNE\",\n            \"bank_display_name\": \"Mahesh Sahakari Bank Ltd, Pune\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"PYTM\",\n            \"bank_name\": \"PAYTM PAYMENTS BANK LTD\",\n            \"bank_display_name\": \"Paytm Payments Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"PSIB\",\n            \"bank_name\": \"PUNJAB AND SIND BANK\",\n            \"bank_display_name\": \"Punjab & Sind Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"PUNB\",\n            \"bank_name\": \"PUNJAB NATIONAL BANK\",\n            \"bank_display_name\": \"Punjab National Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"RATN\",\n            \"bank_name\": \"RBL BANK LIMITED\",\n            \"bank_display_name\": \"RBL Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"STCB\",\n            \"bank_name\": \"SBM BANK INDIA LTD\",\n            \"bank_display_name\": \"Sbm Bank India Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"SHIX\",\n            \"bank_name\": \"SHIVALIK SMALL FINANCE BANK LTD\",\n            \"bank_display_name\": \"Shivalik Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"SIBL\",\n            \"bank_name\": \"THE SOUTH INDIAN BANK LIMITED\",\n            \"bank_display_name\": \"South Indian Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"SCBL\",\n            \"bank_name\": \"STANDARD CHARTERED BANK\",\n            \"bank_display_name\": \"Standard Chartered Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"SBIN\",\n            \"bank_name\": \"STATE BANK OF INDIA\",\n            \"bank_display_name\": \"State Bank of India\",\n            \"bank_logo\": \"https://cashfreelogo.cashfree.com/checkout/SBIN.png\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"SURY\",\n            \"bank_name\": \"SURYODAY SMALL FINANCE BANK LTD\",\n            \"bank_display_name\": \"Suryoday Small Finance Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"TMBL\",\n            \"bank_name\": \"TAMILNAD MERCANTILE BANK LTD\",\n            \"bank_display_name\": \"Tamilnad Mercantile Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"ACUX\",\n            \"bank_name\": \"THE ADARSH CO OP URBAN BANK LTD\",\n            \"bank_display_name\": \"The Adarsh Co Op Urban Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"CNSX\",\n            \"bank_name\": \"THE CHEMBUR NAGARIK SAHAKARI BANK\",\n            \"bank_display_name\": \"The Chembur Nagarik Sahakari Bank\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"JUCX\",\n            \"bank_name\": \"THE JUNAGADH COMMERCIAL CO OP BANK LTD\",\n            \"bank_display_name\": \"The Junagadh Commercial Co Op Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"NCBL\",\n            \"bank_name\": \"THE NATIONAL CO OP BANK LTD\",\n            \"bank_display_name\": \"The National Co Op Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"SPCB\",\n            \"bank_name\": \"THE SURAT PEOPLES CO OP BANK LTD\",\n            \"bank_display_name\": \"The Surat Peoples Co Op Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"VARA\",\n            \"bank_name\": \"THE VARACHHA CO OP BANK LTD\",\n            \"bank_display_name\": \"The Varachha Co-Op Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"ZCBL\",\n            \"bank_name\": \"THE ZOROASTRIAN CO OP BANK LTD\",\n            \"bank_display_name\": \"The Zoroastrian Co-operative Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\"\n            ]\n          },\n          {\n            \"bank_id\": \"USFB\",\n            \"bank_name\": \"UJJIVAN SMALL FINANCE BANK LTD\",\n            \"bank_display_name\": \"Ujjivan Small Finance Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          },\n          {\n            \"bank_id\": \"YESB\",\n            \"bank_name\": \"YES BANK\",\n            \"bank_display_name\": \"Yes Bank Ltd\",\n            \"bank_logo\": \"\",\n            \"auth\": [\n              \"DEBIT_CARD\",\n              \"NET_BANKING\"\n            ]\n          }\n        ]\n      },\n      {\n        \"type\": \"UPI\",\n        \"validate_UPI\": false,\n        \"available_handles\": [\n          {\n            \"handle\": \"indus\",\n            \"application\": \"BHIM Indus Pay\",\n            \"logo_url\": \"\"\n          },\n          {\n            \"handle\": \"upi\",\n            \"application\": \"BHIM\",\n            \"logo_url\": \"https://cashfreelogo.cashfree.com/checkout/upi/Bhim.png\"\n          },\n          {\n            \"handle\": \"axl\",\n            \"application\": \"PhonePe\",\n            \"logo_url\": \"https://cashfreelogo.cashfree.com/checkout/upi/PhonePe.png\"\n          },\n          {\n            \"handle\": \"gocash\",\n            \"application\": \"BHIM\",\n            \"logo_url\": \"\"\n          },\n          {\n            \"handle\": \"vtest\",\n            \"application\": \"Jupiter Money\",\n            \"logo_url\": \"\"\n          },\n          {\n            \"handle\": \"okaxis\",\n            \"application\": \"Google Pay\",\n            \"logo_url\": \"https://cashfreelogo.cashfree.com/checkout/upi/GPay.png\"\n          },\n          {\n            \"handle\": \"boi\",\n            \"application\": \"BHIM BOI UPI\",\n            \"logo_url\": \"https://cashfreelogo.cashfree.com/checkout/upi/BankOfIndia.png\"\n          },\n          {\n            \"handle\": \"ybl\",\n            \"application\": \"PhonePe\",\n            \"logo_url\": \"https://cashfreelogo.cashfree.com/checkout/upi/PhonePe.png\"\n          },\n          {\n            \"handle\": \"ibl\",\n            \"application\": \"PhonePe\",\n            \"logo_url\": \"https://cashfreelogo.cashfree.com/checkout/upi/PhonePe.png\"\n          },\n          {\n            \"handle\": \"cnrb\",\n            \"application\": \"BHIM Canara\",\n            \"logo_url\": \"https://cashfreelogo.cashfree.com/checkout/upi/CanaraBank.png\"\n          },\n          {\n            \"handle\": \"okahdfcbank\",\n            \"application\": \"Google Pay\",\n            \"logo_url\": \"\"\n          },\n          {\n            \"handle\": \"paytm\",\n            \"application\": \"Paytm\",\n            \"logo_url\": \"https://cashfreelogo.cashfree.com/checkout/upi/Paytm.png\"\n          },\n          {\n            \"handle\": \"apl\",\n            \"application\": \"Amazon Pay\",\n            \"logo_url\": \"https://cashfreelogo.cashfree.com/checkout/upi/AmazonPay.png\"\n          }\n        ],\n        \"intentSupport\": [\n          {\n            \"operatingSystem\": \"ANDROID\",\n            \"applications\": [\n              \"AMAZONPAY\",\n              \"PAYTM\",\n              \"GPAY\",\n              \"PHONEPE\",\n              \"DEFAULT\"\n            ]\n          },\n           {\n            \"operatingSystem\": \"IOS\",\n            \"applications\": [\n              \"AMAZONPAY\",\n              \"PAYTM\",\n              \"GPAY\", \"PHONEPE\", \"BHIM\"\n            ]\n          }\n        ],\n        \"qrSupport\": [\n          \"AMAZONPAY\",\n          \"BHIM\"\n        ],\n        \"isCollectSupported\": false,\n        \"isIntentSupported\": true,\n        \"isQrSupported\": false\n      },\n      {\n        \"type\": \"CARD\",\n        \"allowed_card_types\": [\n          \"CREDIT_CARD\",\n          \"DEBIT_CARD\"\n        ]\n      }\n    ]\n  }\n}", "latency": 0, "statusCode": 200, "label": "Test", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "crudKey": "id", "callbacks": []}, {"uuid": "d6f05e54-59ab-4d66-aec8-180412bf506f", "body": "{\n  \"status\": 200,\n  \"message\": \"Payment Options\",\n  \"data\": {\n    \"available_modes\": [\n      {\n        \"type\": \"E_MANDATE\",\n        \"all_banks\": [],\n        \"account_type\": [\n          \"SAVINGS\"\n        ],\n        \"frequent_banks\": []\n      },\n      {\n        \"type\": \"UPI\",\n        \"available_handles\": [\n          {\n            \"logo_url\": \"https://cashfreelogo.cashfree.com/checkout/upi/GPay.png\",\n            \"handle\": \"okicici\",\n            \"application\": \"Google Pay\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"freoicici\",\n            \"application\": \"Freo Pay\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"axb\",\n            \"application\": \"OkCredit\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"jkb\",\n            \"application\": \"BHIM JKB eCash\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"sbipa\",\n            \"application\": \"YONO SBI App\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"dlb\",\n            \"application\": \"BHIM DLB UPI\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"yapl\",\n            \"application\": \"Amazon Pay\"\n          },\n          {\n            \"logo_url\": \"https://cashfreelogo.cashfree.com/checkout/upi/BankOfIndia.png\",\n            \"handle\": \"boi\",\n            \"application\": \"BHIM BOI UPI\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"fincarebank\",\n            \"application\": \"Fincare Bank App\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"ikwik\",\n            \"application\": \"MobiKwik\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"naviaxis\",\n            \"application\": \"Navi\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"centralbank\",\n            \"application\": \"BHIM Cent UPI App\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"pnb\",\n            \"application\": \"BHIM PNB\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"payu\",\n            \"application\": \"PayU Citrus\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"indie\",\n            \"application\": \"Indusind Bank App\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"oksbi\",\n            \"application\": \"Google Pay\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"timecosmos\",\n            \"application\": \"Timepay\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"yesfam\",\n            \"application\": \"Fampay Yes\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"yesg\",\n            \"application\": \"Groww\"\n          },\n          {\n            \"logo_url\": \"https://cashfreelogo.cashfree.com/checkout/upi/DBS.png\",\n            \"handle\": \"dbs\",\n            \"application\": \"DBS Digibank App\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"kotak811\",\n            \"application\": \"Kotak Bank App\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"yespay\",\n            \"application\": \"YesPay Next\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"hsbc\",\n            \"application\": \"Simply Pay\"\n          },\n          {\n            \"logo_url\": \"https://cashfreelogo.cashfree.com/checkout/upi/PhonePe.png\",\n            \"handle\": \"ibl\",\n            \"application\": \"PhonePe\"\n          },\n          {\n            \"logo_url\": \"https://cashfreelogo.cashfree.com/checkout/upi/CanaraBank.png\",\n            \"handle\": \"cnrb\",\n            \"application\": \"BHIM Canara\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"abcdicici\",\n            \"application\": \"Aditya Birla Capital Digital App\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"slicepay\",\n            \"application\": \"Slice Pay\"\n          },\n          {\n            \"logo_url\": \"https://cashfreelogo.cashfree.com/checkout/upi/Paytm.png\",\n            \"handle\": \"paytm\",\n            \"application\": \"Paytm\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"pingpay\",\n            \"application\": \"Samsung\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"tapicici\",\n            \"application\": \"TataNeu\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"zoicici\",\n            \"application\": \"Zomato\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"goaxb\",\n            \"application\": \"Kiwi App\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"ptaxis\",\n            \"application\": \"Paytm Axis\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"fam\",\n            \"application\": \"Fampay\"\n          },\n          {\n            \"logo_url\": \"https://cashfreelogo.cashfree.com/checkout/upi/GPay.png\",\n            \"handle\": \"okaxis\",\n            \"application\": \"Google Pay\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"superyes\",\n            \"application\": \"Super Money\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"jio\",\n            \"application\": \"MyJio UPI\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"jupiteraxis\",\n            \"application\": \"Jupiter Money\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"shriramhdfcbank\",\n            \"application\": \"Shriram One App\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"equitas\",\n            \"application\": \"Equitas Small Finance Bank Ltd\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"freecharge\",\n            \"application\": \"Freecharge\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"mboi\",\n            \"application\": \"BOI Mobile Omni Neo Bank\"\n          },\n          {\n            \"logo_url\": \"https://cashfreelogo.cashfree.com/checkout/upi/Bhim.png\",\n            \"handle\": \"upi\",\n            \"application\": \"BHIM\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"fifederal\",\n            \"application\": \"Fi Money\"\n          },\n          {\n            \"logo_url\": \"https://cashfreelogo.cashfree.com/checkout/upi/PhonePe.png\",\n            \"handle\": \"axl\",\n            \"application\": \"PhonePe\"\n          },\n          {\n            \"logo_url\": \"https://cashfreelogo.cashfree.com/checkout/upi/IciciBank.png\",\n            \"handle\": \"icici\",\n            \"application\": \"ICICI iMobile\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"iob\",\n            \"application\": \"Indian Overseas Bank App\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"waicici\",\n            \"application\": \"WhatsApp\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"ptyes\",\n            \"application\": \"Paytm Yes\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"barodampay\",\n            \"application\": \"BOB World UPI\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"ptsbi\",\n            \"application\": \"Paytm SBI\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"sib\",\n            \"application\": \"SIB Mirror +\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"mahb\",\n            \"application\": \"Mahamobile Plus App\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"niyoicici\",\n            \"application\": \"Go Niyo\"\n          },\n          {\n            \"logo_url\": \"https://cashfreelogo.cashfree.com/checkout/upi/PhonePe.png\",\n            \"handle\": \"ybl\",\n            \"application\": \"PhonePe\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"pz\",\n            \"application\": \"PayZapp\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"sliceaxis\",\n            \"application\": \"Slice\"\n          },\n          {\n            \"logo_url\": \"https://cashfreelogo.cashfree.com/checkout/upi/GPay.png\",\n            \"handle\": \"okhdfcbank\",\n            \"application\": \"Google Pay\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"abfspay\",\n            \"application\": \"Bajaj Finserv\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"sbi\",\n            \"application\": \"BHIM SBI Pay\"\n          },\n          {\n            \"logo_url\": \"\",\n            \"handle\": \"kotak\",\n            \"application\": \"Kotak Bank App\"\n          },\n          {\n            \"logo_url\": \"https://cashfreelogo.cashfree.com/checkout/upi/AmazonPay.png\",\n            \"handle\": \"apl\",\n            \"application\": \"Amazon Pay\"\n          }\n        ],\n        \"validate_UPI\": true,\n        \"isCollectSupported\": true,\n        \"isIntentSupported\": true,\n        \"isQrSupported\": true,\n        \"intentSupport\": [\n          {\n            \"applications\": [\n              \"PHONEPE\",\n              \"GPAY\",\n              \"PAYTM\",\n              \"BHIM\",\n              \"AMAZONPAY\",\n              \"DEFAULT\"\n            ],\n            \"operatingSystem\": \"ANDROID\"\n          },\n          {\n            \"applications\": [\n              \"PHONEPE\",\n              \"GPAY\",\n              \"PAYTM\",\n              \"BHIM\"\n            ],\n            \"operatingSystem\": \"IOS\"\n          }\n        ],\n        \"qrSupport\": [\n          \"PHONEPE\",\n          \"PAYTM\",\n          \"AMAZONPAY\",\n          \"GPAY\"\n        ]\n      }\n    ]\n  }\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "crudKey": "id", "callbacks": []}], "responseMode": "DISABLE_RULES", "type": "http"}, {"uuid": "3c664340-ea20-11eb-bc47-dfb7587cf92c", "documentation": "", "method": "get", "endpoint": "subscription", "responses": [{"uuid": "3c664341-ea20-11eb-bc47-dfb7587cf92c", "body": "{\n  \"status\": 200,\n  \"message\": \"Subscription Details\",\n  \"data\": {\n    \"subscription_meta\": {\n      \"subreference_id\": 74203,\n      \"subscription_id\": \"SBCCheckouttestPer01\",\n      \"status\": \"INITIALIZED\",\n      \"start_date\": \"2022-06-27 13:12:59\",\n      \"end_date\": \"2024-06-27 12:40:38\",\n      \"source\": \"API_NON_SEAMLESS\",\n      \"authorization_amount\": 100000,\n      \"calculated_first_charge_date\": {\n        \"CARD\": \"2022-07-11\",\n        \"E_MANDATE\": \"2022-07-11\",\n        \"PHYSICAL_MANDATE\": \"2022-07-11\",\n        \"UPI\": \"2022-07-11\"\n      }\n    },\n    \"customer_meta\": {\n      \"customer_name\": \"<PERSON>\",\n      \"customer_email\": \"<EMAIL>\",\n      \"customer_phone\": \"**********\"\n    }\n  }\n}", "latency": 0, "statusCode": 200, "label": "With Authorization Amount", "headers": [{"key": "X-Request-ID", "value": "adace6a14b1000f6a5c3408d96dcb522"}, {"key": "Access-Control-Expose-Headers", "value": "X-Request-ID"}], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "8be51ac2-cf2f-4b16-92bb-6e5a0fbc4971", "body": "{}", "latency": 0, "statusCode": 504, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "c08a4810-011d-11ec-829c-157175d4987c", "body": "{\n  \"status\": 200,\n  \"message\": \"Subscription Details\",\n  \"data\": {\n    \"subscription_meta\": {\n      \"subreference_id\": 66111,\n      \"status\": \"ACTIVE\",\n      \"start_date\": \"2022-03-29 13:14:47\",\n      \"end_date\": \"2024-03-29 13:14:47\",\n      \"calculated_first_charge_date\": {\n\t\t\t\t\"CARD\": \"2022-05-12\",\n\t\t\t\t\"E_MANDATE\": \"2022-05-12\",\n\t\t\t\t\"UPI\": \"2022-05-12\"\n\t\t\t}\n    },\n    \"customer_meta\": {\n      \"customer_name\": \"\",\n      \"customer_email\": \"<EMAIL>\",\n      \"customer_phone\": \"8092504655\"\n    }\n  }\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [{"key": "X-Request-Id", "value": "adace6a14b1000f6a5c3408d96dcb522"}, {"key": "Access-Control-Expose-Headers", "value": "X-Request-Id"}], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "7f8ad4de-36be-4275-9203-6c21530ee840", "body": "{}", "latency": 0, "statusCode": 400, "label": "", "headers": [{"key": "X-Request-ID", "value": "adace6a14b1000f6a5c3408d96dcb522"}, {"key": "Access-Control-Expose-Headers", "value": "X-Request-ID"}], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "4ada280e-6d15-4890-9ba7-d897e1d4709e", "body": "{\n  \"status\": 200,\n  \"message\": \"Subscription Details\",\n  \"data\": {\n    \"subscription_meta\": {\n      \"subreference_id\": 63046,\n      \"status\": \"INITIALIZED\",\n      \"start_date\": \"2022-03-02 12:20:20\",\n      \"end_date\": \"2024-03-02 12:20:20\",\n      \"calculated_first_charge_date\": {\n\t\t\t\t\"CARD\": \"2022-05-12\",\n\t\t\t\t\"E_MANDATE\": \"2022-05-11\",\n\t\t\t\t\"UPI\": \"2022-05-12\"\n\t\t\t}\n    },\n    \"customer_meta\": {\n      \"customer_name\": \"Raamdaas\",\n      \"customer_email\": \"<EMAIL>\",\n      \"customer_phone\": \"7759878629\"\n    }\n  }\n}", "latency": 0, "statusCode": 200, "label": "Without Authorization Amount", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "e9cbbe34-9352-40e2-907b-fd53aaf4cac8", "body": "{\n  \"status\": 200,\n  \"message\": \"Subscription Details\",\n  \"data\": {\n    \"subscription_meta\": {\n\n      \"subreference_id\": 74203,\n      \"subscription_id\": \"SBCCheckouttestPer01\",\n      \"status\": \"INITIALIZED\",\n      \"start_date\": \"2022-06-27 13:12:59\",\n      \"end_date\": \"2024-06-27 12:40:38\",\n      \"source\": \"API_NON_SEAMLESS\",\n      \"authorization_amount\": 1,\n      \"calculated_first_charge_date\": {\n        \"CARD\": \"2022-07-11\",\n        \"E_MANDATE\": \"2022-07-11\",\n        \"PHYSICAL_MANDATE\": \"2022-07-11\",\n        \"UPI\": \"2022-07-11\"\n      },\n      \"tpv_enabled\": false,\n\t\t\t\"payment_instrument_details\": {\n\t\t\t\t\"account_number\": \"1232343548548574857\",\n\t\t\t\t\"account_holder_name\": \"<PERSON><PERSON>ttal\",\n\t\t\t\t\"bank_id\": \"BDBL\",\n\t\t\t\t\"account_type\": \"CURRENT\",\n\t\t\t\t\"bank_name\": \"HDFC Bank\"\n\t\t\t}\n    },\n    \"customer_meta\": {\n      \"customer_name\": \"John Doe\",\n      \"customer_email\": \"<EMAIL>\",\n      \"customer_phone\": \"**********\"\n    }\n  }\n}", "latency": 0, "statusCode": 200, "label": "With TPV", "headers": [{"key": "X-Request-ID", "value": "adace6a14b1000f6a5c3408d96dcb522"}, {"key": "Access-Control-Expose-Headers", "value": "X-Request-ID"}], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "60d7aa6f-42ef-4491-9c29-e354852119a9", "body": "{\n  \"status\": 200,\n  \"message\": \"Subscription Details\",\n  \"data\": {\n    \"subscription_meta\": {\n      \"subreference_id\": 74203,\n      \"subscription_id\": \"SBCCheckouttestPer01\",\n      \"status\": \"LINK_EXPIRED\",\n      \"start_date\": \"2022-06-27 13:12:59\",\n      \"end_date\": \"2024-06-27 12:40:38\",\n      \"source\": \"API_NON_SEAMLESS\",\n      \"authorization_amount\": 1,\n      \"calculated_first_charge_date\": {\n        \"CARD\": \"2022-07-11\",\n        \"E_MANDATE\": \"2022-07-11\",\n        \"PHYSICAL_MANDATE\": \"2022-07-11\",\n        \"UPI\": \"2022-07-11\"\n      },\n      \"tpv_enabled\": false,\n\t\t\t\"payment_instrument_details\": {\n\t\t\t\t\"account_number\": \"1232343548548574857\",\n\t\t\t\t\"account_holder_name\": \"<PERSON><PERSON> Mittal\",\n\t\t\t\t\"bank_id\": \"BDBL\",\n\t\t\t\t\"account_type\": \"CURRENT\",\n\t\t\t\t\"bank_name\": \"HDFC Bank\"\n\t\t\t}\n    },\n    \"customer_meta\": {\n      \"customer_name\": \"John Doe\",\n      \"customer_email\": \"<EMAIL>\",\n      \"customer_phone\": \"**********\"\n    }\n  }\n}", "latency": 0, "statusCode": 200, "label": "Link Expired", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "crudKey": "id", "callbacks": []}], "responseMode": null, "type": "http"}, {"uuid": "50c9d6f0-ef96-11eb-bc47-dfb7587cf92c", "documentation": "", "method": "get", "endpoint": "plan", "responses": [{"uuid": "50c9d6f1-ef96-11eb-bc47-dfb7587cf92c", "body": "{\n  \"status\": 200,\n  \"message\": \"Plan Details\",\n  \"data\": {\n    \"plan_meta\": {\n      \"plan_id\": \"UPI-SUBMIT-PER\",\n      \"plan_name\": \"UPI-SUBMIT-PER\",\n      \"max_cycles\": 0,\n      \"added_on\": \"2022-06-27T12:39:01\"\n    },\n    \"plan_details\": {\n      \"type\": \"PERIODIC\",\n      \"intervals\": 1,\n      \"interval_type\": \"MONTH\",\n      \"amount\": 4999.99,\n      \"currency\": \"INR\",\n      \"active\": true\n    }\n  }\n}", "latency": 0, "statusCode": 200, "label": "Periodic < 5000", "headers": [{"key": "Access-Control-Expose-Headers", "value": "X-Request-ID"}, {"key": "X-Request-ID", "value": "adace6a14b1000f6a5c3408d96dcb523"}], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "a9cca106-7ae0-4f00-b866-1ca93312c577", "body": "{\n  \"status\": 200,\n  \"message\": \"Plan Details\",\n  \"data\": {\n    \"plan_meta\": {\n      \"plan_id\": \"UPI-SUBMIT-PER\",\n      \"plan_name\": \"UPI-SUBMIT-PER\",\n      \"max_cycles\": 0,\n      \"added_on\": \"2022-06-27T12:39:01\"\n    },\n    \"plan_details\": {\n      \"type\": \"PERIODIC\",\n      \"intervals\": 1,\n      \"interval_type\": \"MONTH\",\n      \"amount\": 4999.99,\n      \"currency\": \"INR\",\n      \"active\": true\n    }\n  }\n}", "latency": 0, "statusCode": 200, "label": "Periodic < 5000 (copy)", "headers": [{"key": "Access-Control-Expose-Headers", "value": "X-Request-ID"}, {"key": "X-Request-ID", "value": "adace6a14b1000f6a5c3408d96dcb523"}], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "df43e737-0652-4589-925b-1ba6303cfe90", "body": "{\n    \"status\": \"OK\",\n    \"message\": \"Plan Details\",\n    \"data\": {\n        \"plan_meta\": {\n            \"plan_id\": \"p123\",\n            \"plan_name\": \"New Test Plan\",\n            \"max_cycles\": 0,\n            \"added_on\": \"2021-07-22T12:51:49\"\n        },\n        \"plan_details\": {\n            \"type\": \"PERIODIC\",\n            \"intervals\": 1,\n            \"interval_type\": \"month\",\n            \"amount\": 50000,\n            \"authorization_amount\": \"\",\n            \"currency\": \"INR\",\n            \"active\": false\n        }\n    }\n}", "latency": 0, "statusCode": 200, "label": "Periodic > 5000", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "99ee3f90-011d-11ec-829c-157175d4987c", "body": "{\n    \"status\": 200,\n    \"message\": \"Plan Details\",\n    \"data\": {\n        \"plan_meta\": {\n            \"plan_id\": \"p123\",\n            \"plan_name\": \"New Test Plan\",\n            \"max_cycles\": 0,\n            \"added_on\": \"2021-07-22T12:51:49\"\n        },\n        \"plan_details\": {\n            \"type\": \"ON_DEMAND\",\n            \"intervals\": 1,\n            \"interval_type\": \"month\",\n            \"amount\": 10000,\n            \"currency\": \"INR\",\n            \"active\": false\n        }\n    }\n}", "latency": 0, "statusCode": 200, "label": "On Demand", "headers": [{"key": "Access-Control-Expose-Headers", "value": "X-Request-ID"}, {"key": "X-Request-ID", "value": "adace6a14b1000f6a5c3408d96dcb523"}], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "06ee2353-586d-44d3-be78-790b0cff1daf", "body": "{}", "latency": 0, "statusCode": 400, "label": "", "headers": [{"key": "Access-Control-Expose-Headers", "value": "X-Request-ID"}, {"key": "X-Request-ID", "value": "adace6a14b1000f6a5c3408d96dcb523"}], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}], "responseMode": null, "type": "http"}, {"uuid": "2ebe5640-ef9a-11eb-bc47-dfb7587cf92c", "documentation": "", "method": "get", "endpoint": "merchant", "responses": [{"uuid": "2ebe5641-ef9a-11eb-bc47-dfb7587cf92c", "body": "{\n\t\"status\": 200,\n\t\"message\": \"Merchant Details\",\n\t\"data\": {\n\t\t\"merchant_meta\": {\n\t\t\t\"merchant_id\": 17,\n\t\t\t\"merchant_name\": \"CashFree Merchant\",\n\t\t\t\"merchant_logo\": \"https://cashfreelogo.cashfree.com/4a21ec426bd8982d2d63b401cab834b58931c2a0509f2d975fc4455e00f229d2\",\n\t\t\t\"merchant_email\": \"<EMAIL>\"\n\t\t},\n\t\t\"merchant_page_pref\": {\n\t\t\t\"theme_color\": \"692467\",\n\t\t\t\"button_color\": \"940D0D\",\n\t\t\t\"hover_color\": \"796FFF\",\n\t\t\t\"logo_large\": \"http://4vector.com/i/free-vector-cashfree-0_072416_cashfree-0.png\"\n\t\t}\n\t}\n}\n\n\n", "latency": 0, "statusCode": 200, "label": "", "headers": [{"key": "Access-Control-Expose-Headers", "value": "X-Request-ID"}, {"key": "X-Request-ID", "value": "adace6a14b1000f6a5c3408d96dcb523"}], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "e2f1f1bf-6647-4c9b-b8eb-8db2aa6858d4", "body": "{}", "latency": 0, "statusCode": 500, "label": "", "headers": [{"key": "Access-Control-Expose-Headers", "value": "X-Request-ID"}, {"key": "X-Request-ID", "value": "adace6a14b1000f6a5c3408d96dcb523"}], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}], "responseMode": null, "type": "http"}, {"uuid": "6c214d73-729b-45bf-85ca-ed8a2f02132c", "documentation": "", "method": "post", "endpoint": "create-auth", "responses": [{"uuid": "f2353018-33ad-45b0-a8f6-efbcf89f05e0", "body": "{\n  \"status\": 200,\n  \"message\": \"Authorization created successfully\",\n  \"data\": {\n    \"sub_reference_id\": 924,\n    \"return_url\": \"http://devo.gocashfree.com/billpay/subscription/simnpcisbc/return?sbcdata=969JCN4MzUIJiOicGbhJCLiQ1VKJiOiAXe0Jye.Xw0Xf3EjOiQWS05WYoNmcl1mIskDNxEjOiQWS05WZtlXYwJCL3AjMxojIklEa0VXYiwCNykjOiQWSlNmblJXZmVmUiV3cisnOiwkUV9lTSVFVFJ1XHB1XIRVVBJye.tUe7gY7Z_7jk2i6Ze1ZvAqX21-mdynvvu6KIXo5OPSExFDLySX_TgAdSQhGikkyq_C\",\n    \"auth_status\": \"INITIALIZED\",\n    \"subscription_status\": \"INITIALIZED\",\n    \"payment_id\": 1149,\n    \"gateway_data\": {\n      \"auth_id\": 1207,\n      \"response_type\": \"form\",\n      \"redirect_url\": \"https://simnpciRedirect.com\",\n      \"redirect_method\": \"post\",\n      \"data\": {\n        \"AuthMode\": \"NET_BANKING\",\n        \"BankID\": \"SBIN\",\n        \"CheckSumVal\": \"simcheckSum\",\n        \"MandateReqDoc\": \"Mandate Req Document\",\n        \"MerchantID\": \"Bagdol99237813910282\"\n      }\n    }\n  }\n  \n  \n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "98a6ce41-**************-3f214ba6a4d7", "body": "{\n  \"status\": \"OK\",\n  \"message\": \"Authorization created successfully\",\n  \"data\": {\n    \"auth_id\": 1207,\n    \"sub_reference_id\": 924,\n    \"return_url\": \"http://devo.gocashfree.com/billpay/subscription/simnpcisbc/return?sbcdata=969JCN4MzUIJiOicGbhJCLiQ1VKJiOiAXe0Jye.Xw0Xf3EjOiQWS05WYoNmcl1mIskDNxEjOiQWS05WZtlXYwJCL3AjMxojIklEa0VXYiwCNykjOiQWSlNmblJXZmVmUiV3cisnOiwkUV9lTSVFVFJ1XHB1XIRVVBJye.tUe7gY7Z_7jk2i6Ze1ZvAqX21-mdynvvu6KIXo5OPSExFDLySX_TgAdSQhGikkyq_C\",\n    \"auth_status\": \"INITIALIZED\",\n    \"subscription_status\": \"INITIALIZED\",\n    \"payment_id\": 1149,\n    \"gateway_data\": {\n      \"response_type\": \"form\",\n      \"redirect_url\": \"https://simnpciRedirect.com\",\n      \"redirect_method\": \"post\",\n      \"data\": {\n        \"AuthMode\": \"NET_BANKING\",\n        \"BankID\": \"SBIN\",\n        \"CheckSumVal\": \"simcheckSum\",\n        \"MandateReqDoc\": \"Mandate Req Document\",\n        \"MerchantID\": \"Bagdol99237813910282\"\n      }\n    }\n  }\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "db9b8d64-67c2-4e0e-ae70-d335087655fa", "body": "{}", "latency": 0, "statusCode": 400, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "0c256558-afa5-4daa-be64-5108c1d38735", "body": "{\n  \"status\": \"OK\",\n  \"message\": \"Authorization created successfully\",\n  \"data\": {\n    \"auth_id\": 33651,\n    \"sub_reference_id\": 72412,\n    \"return_url\": \"\",\n    \"auth_status\": \"INITIALIZED\",\n    \"subscription_status\": \"INITIALIZED\",\n    \"payment_id\": 98101,\n    \"gateway_data\": {\n      \"response_type\": \"redirect\",\n      \"redirect_url\": \"https://test.cashfree.com/pgsim/simulator\",\n      \"redirect_method\": \"post\",\n      \"data\": {\n        \"amount\": \"1000.00\",\n        \"orderCurrency\": \"INR\",\n        \"paymentMode\": \"SBC_UPI\",\n        \"signature\": \"ByiQbB+cnzCnZiZ+XVaozB6A6RXD5DgpJ6j24sF5N2I=\",\n        \"status\": \"FAILED\",\n        \"transactionId\": \"33651\"\n      }\n    }\n  }\n}", "latency": 0, "statusCode": 200, "label": "UPI Auth", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "75ee4820-1318-4056-8beb-e40706a5a6df", "body": "{\n  \"status\": 200,\n  \"message\": \"Authorization created successfully\",\n  \"data\": {\n    \"auth_id\": 33911,\n    \"sub_reference_id\": 73833,\n    \"return_url\": \"https://beta.cashfree.com/billpay/subscription/simsbc/return?sbcdata=bR9JCN4MzUIJiOicGbhJCLiQ1VKJiOiAXe0Jye.gM913NyojIklEduFGajJXZtJCL0czM4kjOiQWS05WZtlXYwJCLxETOzMjOiQWSoRXdhJCLzMDOzcjOiQWSlNmblJXZmVmUiV3cisnOiwkUV9lTSVFVFJ1XHB1XIRVVBJye.L83xVcnJWzKNPmnO8-XBeTNMcJZdjSu48y0lZeb2bfrYIAALHQTk3xRrorg8TjLit_\",\n    \"auth_status\": \"INITIALIZED\",\n    \"subscription_status\": \"INITIALIZED\",\n    \"payment_id\": 98374,\n    \"gateway_data\": {\n      \"response_type\": \"redirect\",\n      \"redirect_url\": \"https://test.cashfree.com/pgsim/simulator\",\n      \"redirect_method\": \"post\",\n      \"data\": {\n        \"amount\": \"1.00\",\n        \"orderCurrency\": \"INR\",\n        \"paymentMode\": \"SBC_CREDIT_CARD\",\n        \"returnUrl\": \"https://beta.cashfree.com/billpay/subscription/simsbc/return?sbcdata=bR9JCN4MzUIJiOicGbhJCLiQ1VKJiOiAXe0Jye.gM913NyojIklEduFGajJXZtJCL0czM4kjOiQWS05WZtlXYwJCLxETOzMjOiQWSoRXdhJCLzMDOzcjOiQWSlNmblJXZmVmUiV3cisnOiwkUV9lTSVFVFJ1XHB1XIRVVBJye.L83xVcnJWzKNPmnO8-XBeTNMcJZdjSu48y0lZeb2bfrYIAALHQTk3xRrorg8TjLit_\",\n        \"signature\": \"1SLvGTEr/T2XiAKkcMOCQV8S5bWMM1kJFxFbDjcU46s=\",\n        \"transactionId\": \"885306313\"\n      }\n    }\n  }\n}", "latency": 0, "statusCode": 200, "label": "Card Success", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "a106b3bb-48a1-42b3-a92d-fcc8567fc4ed", "body": "{\n  \"status\": 200,\n  \"message\": \"Authorization created successfully\",\n  \"data\": {\n    \"auth_id\": 33651,\n    \"sub_reference_id\": 924,\n    \"return_url\": \"http://devo.gocashfree.com/billpay/subscription/simnpcisbc/return?sbcdata=969JCN4MzUIJiOicGbhJCLiQ1VKJiOiAXe0Jye.Xw0Xf3EjOiQWS05WYoNmcl1mIskDNxEjOiQWS05WZtlXYwJCL3AjMxojIklEa0VXYiwCNykjOiQWSlNmblJXZmVmUiV3cisnOiwkUV9lTSVFVFJ1XHB1XIRVVBJye.tUe7gY7Z_7jk2i6Ze1ZvAqX21-mdynvvu6KIXo5OPSExFDLySX_TgAdSQhGikkyq_C\",\n    \"auth_status\": \"INITIALIZED\",\n    \"subscription_status\": \"INITIALIZED\",\n    \"payment_id\": 1149,\n    \"gateway_data\": {\n      \"response_type\": \"form\",\n      \"redirect_url\": \"https://simnpciRedirect.com\",\n      \"redirect_method\": \"post\",\n      \"data\": {\n        \"AuthMode\": \"NET_BANKING\",\n        \"BankID\": \"SBIN\",\n        \"qrData\":\"upi://mandate?pa=cfsubs@icici&pn=Cashfree%20Test%20Subs&tr=EZM2023031820514381234024&am=1.00&cu=INR&orgid=400011&mc=5411&purpose=14&tn=Cashfree%20Payments&validitystart=********&validityend=********&amrule=MAX&recur=ASPRESENTED&rev=Y&share=Y&block=N&txnType=CREATE&mode=13&sign=MEUCIBRiWCQgR8YIOqFMzXSi4nv+tgvZiBJES4/+rDRgv2O9AiEA9YvbfVwBgKGyeHwnCmOzLfzo\\r\\nMOcli82kGUuXfjZpEH4=\\r\\n\",\n        \"CheckSumVal\": \"simcheckSum\",\n        \"MandateReqDoc\": \"Mandate Req Document\",\n        \"MerchantID\": \"Bagdol99237813910282\"\n      }\n    }\n  }\n  \n  \n}", "latency": 0, "statusCode": 200, "label": "UPI QR", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}], "responseMode": null, "type": "http"}, {"uuid": "6a2acfeb-5263-41e7-a79a-e9b458123515", "documentation": "", "method": "post", "endpoint": "update_authorize_subscription", "responses": [{"uuid": "29464004-a654-4e9d-bb6d-02e3975c2181", "body": "{\n  \"status\": 200,\n  \"message\": \"Subscription mandate updated successfuly\",\n  \"data\": {\n    \"status\": \"SUCCESS\"\n  }\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}], "responseMode": null, "type": "http"}, {"uuid": "dfe3a739-c169-4364-b697-316d316c3a56", "documentation": "", "method": "get", "endpoint": "pgCreds/:accountType/paymentType/:paymentType", "responses": [{"uuid": "********-8b6a-4033-b88e-7161a3d4b1f0", "body": "{\n  \"status\": 200,\n  \"message\": \"Processing Partner\",\n  \"data\": {\n    \"utility_code\": \"2067Mid\",\n    \"corporate_name\": \"Cashfree Payments Private Limited\"\n  }\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}], "responseMode": null, "type": "http"}, {"uuid": "7d005410-a775-4ed3-b13b-a816a67956db", "documentation": "", "method": "post", "endpoint": "verify-upi", "responses": [{"uuid": "0a0d304c-0eae-42c4-9fd4-836d20cd9917", "body": "{\n  \"status\": 422,\n  \"message\": \"Verfication failed\",\n  \"data\": {\n    \"is_valid\": false,\n    \"validation_message\": \"\"\n  }\n}", "latency": 0, "statusCode": 422, "label": "Internal API Error", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "de36eb7e-d9cd-4a90-a487-bb11f9d2be45", "body": "{\n  \"status\": 200,\n  \"message\": \"Subscription Details\",\n  \"data\": {\n    \"name_at_bank\": \"JOHN SNOW\",\n    \"is_valid\": true\n  }\n}", "latency": 0, "statusCode": 200, "label": "Verification Successful", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "e08f863b-2d1d-4a90-8aa4-065c1367f298", "body": "{\n  \"status\": 200,\n  \"message\": \"Verification Successful\",\n  \"data\": {\n    \"validation_message\": \"Account does not exist\",\n    \"is_valid\": false\n  }\n}", "latency": 0, "statusCode": 200, "label": "<PERSON><PERSON>, Account Doesnt exist", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "07a4b3a6-6131-46ec-b413-22518541e428", "body": "{\n  \"status\": 400,\n  \"message\": \"VPA Verification Failed\",\n  \"data\": {\n    \"is_valid\": false,\n    \"verification_message\": \"Unknown error occured!\"\n  }\n}", "latency": 0, "statusCode": 400, "label": "Bad Request", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}], "responseMode": null, "type": "http"}, {"uuid": "c07f8749-1931-43c4-992e-bfb7218f6395", "documentation": "", "method": "post", "endpoint": "validate-auth-attempt", "responses": [{"uuid": "51447e4b-8015-4c4f-8d3e-e8f3e1371132", "body": "{\n  \"status\": 200,\n  \"message\": \"Validate Auth Attempt\",\n  \"data\": {\n    \"is_valid\": true,\n    \"validation_message\": \"success/failure message\"\n  }\n}", "latency": 0, "statusCode": 200, "label": "Validation Success", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "ef013010-6e32-4830-bc51-745674c28a14", "body": "{\n  \"status\": 200,\n  \"message\": \"Validate Auth Attempt\",\n  \"data\": {\n    \"is_valid\": false,\n    \"validation_message\": \"Mandate creation on this account is not allowed by NPCI\"\n  }\n}", "latency": 0, "statusCode": 200, "label": " Validation Failed", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "8609a637-10a6-4577-8184-542bf7311b69", "body": "{\"message\":\"Not Found\"}", "latency": 0, "statusCode": 404, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}], "responseMode": null, "type": "http"}, {"uuid": "77a80726-57e4-4689-83f5-abd61592fc96", "documentation": "", "method": "post", "endpoint": "amplitude-event", "responses": [{"uuid": "c9b4f9db-fe36-4474-90d4-3c5726da776f", "body": "{\n  \"status\": \"OK\",\n  \"message\": \"Amplitude event tracked successfully\",\n  \"data\": {\n  }\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}], "responseMode": null, "type": "http"}, {"uuid": "d72d807b-6af2-49f8-810e-c4e85d9bd82e", "documentation": "", "method": "get", "endpoint": "subscription-and-auth-details", "responses": [{"uuid": "bcbab4be-951f-48ec-b710-7c37ddf139ae", "body": "{\n  \"status\": \"OK\",\n  \"message\": \"Get Subscription and auth Details\",\n  \"data\": {\n    \"subscription_id\": \"test-January-30-2023-04-20-00-ss\",\n    \"sub_reference_id\": 111889,\n    \"selected_mode\": \"UPI\",\n    \"auth_status\": \"ACTIVE\",\n    \"auth_amount\": 1,\n    \"sub_status\": \"ACTIVE\",\n    \"umn\": \"vlDB9GmC6MrpvhEXtQcgfJwNltYygo@upi\",\n    \"source\": \"API_NON_SEAMLESS\",\n    \"authFailureReason\": \"\",\n    \"start_date\": \"2023-01-30 16:20:17\",\n    \"end_date\": \"2025-01-30 16:20:01\",\n    \"return_url\": \"google.com\",\n    \"redirectOnCheckoutFailure\": true,\n    \"plan_name\": \"Plan 1673269416, nice plan\",\n    \"max_cycles\": 0,\n    \"type\": \"ON_DEMAND\",\n    \"intervals\": 0,\n    \"interval_type\": \"\",\n    \"amount\": 1,\n    \"currency\": \"INR\",\n    \"redirection_payload\": {\n      \"cf_subReferenceId\": 111889,\n      \"cf_subscriptionId\": \"test-January-30-2023-04-20-00-ss\",\n      \"cf_authAmount\": 1,\n      \"cf_referenceId\": \"N/A\",\n      \"cf_status\": \"ACTIVE\",\n      \"cf_message\": \"Subscription Authorised Successfully\",\n      \"signature\": \"/Q6Hds333CQ6aVNeO2ZpOvKbKfGx7bWoHBwAut11dwI=\",\n      \"cf_umn\": \"vlDB9GmC6MrpvhEXtQcgfJwNltYygo@upi\",\n      \"cf_mode\": \"SBC_UPI\",\n      \"cf_subscriptionPaymentId\": 123033,\n      \"cf_checkoutStatus\": \"SUCCESS\"\n    }\n  }\n}", "latency": 0, "statusCode": 200, "label": "UPI Success", "headers": [{"key": "X-Request-ID", "value": "adace6a14b1000f6a5c3408d96dcb523"}, {"key": "Access-Control-Expose-Headers", "value": "X-Request-ID"}], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "2bfca3a8-fc00-4143-8ad7-5a08cfde5318", "body": "{\n  \"status\": \"OK\",\n  \"message\": \"Get Subscription and auth Details\",\n  \"data\": {\n    \"subscription_id\": \"test_checkout_return_00031\",\n    \"sub_reference_id\": 70781,\n    \"selected_mode\": \"CARD\",\n    \"auth_status\": \"FAILED\",\n    \"authFailureReason\": \"User has rejected the transaction on pre-login page\",\n    \"start_date\": \"2022-05-17 11:09:55\",\n    \"end_date\": \"2024-05-17 23:59:59\",\n    \"redirectOnCheckoutFailure\": true,\n    \"source\": \"API_NON_SEAMLESS\",\n    \"return_url\": \"https://webhook.site/8f972515-186e-4e49-9296-22759b2a0271\",\n    \"redirection_payload\": {\n      \"cf_subReferenceId\": 73967,\n      \"cf_subscriptionId\": \"test_checkout_001ywy\",\n      \"cf_authAmount\": 1,\n      \"cf_referenceId\": \"N/A\",\n      \"cf_status\": \"\",\n      \"cf_message\": \"\",\n      \"signature\": \"uUi1Elcktgn0st8yScO1J58gyMvTteEZ08bJaoqSwFI=\",\n      \"cf_umrn\": \"ICIC70212102000056683\",\n      \"cf_mode\": \"SBC_CARD\",\n      \"cf_subscriptionPaymentId\": 98472,\n      \"cf_checkoutStatus\": \"\"\n    }\n  }\n}", "latency": 0, "statusCode": 200, "label": "Card Failure", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "de3f351e-4113-4554-a9e1-fcd9cd2121c0", "body": "{\n  \"status\": \"OK\",\n  \"message\": \"Get Subscription and auth Details\",\n  \"data\": {\n    \"subscription_id\": \"test_checkout_001ywy\",\n    \"sub_reference_id\": 73967,\n\t\t\"selected_mode\": \"CARD\",\n\t\t\"auth_status\": \"ACTIVE\",\n    \"sub_status\": \"ACTIVE\",\n    \"umrn\": \"ICIC70212102000056683\",\n    \"authFailureReason\": \"\",\n    \"start_date\": \"2022-06-23 23:43:39\",\n    \"source\": \"API_NON_SEAMLESS\",\n    \"end_date\": \"2024-06-23 23:59:59\",\n    \"auth_amount\": \"10\",\n    \"redirectOnCheckoutFailure\": true,\n    \"return_url\": \"https://webhook.site/8f972515-186e-4e49-9296-22759b2a0271\",\n    \"redirection_payload\": {\n      \"cf_subReferenceId\": 73967,\n      \"cf_subscriptionId\": \"test_checkout_001ywy\",\n      \"cf_authAmount\": 1,\n      \"cf_referenceId\": \"N/A\",\n      \"cf_status\": \"\",\n      \"cf_message\": \"\",\n      \"signature\": \"uUi1Elcktgn0st8yScO1J58gyMvTteEZ08bJaoqSwFI=\",\n      \"cf_umrn\": \"ICIC70212102000056683\",\n      \"cf_mode\": \"SBC_CARD\",\n      \"cf_subscriptionPaymentId\": 98472,\n      \"cf_checkoutStatus\": \"\"\n    }\n  }\n}\n", "latency": 0, "statusCode": 200, "label": "Card Success", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "b0bc2618-8cc8-4d4a-857e-44d2eb60f451", "body": "{\n  \"status\": \"OK\",\n  \"message\": \"Get Subscription and auth Details\",\n  \"data\": {\n    \"subscription_id\": \"test_checkout_001ywy\",\n    \"sub_reference_id\": 73967,\n    \"selected_mode\": \"E_MANDATE\",\n    \"auth_status\": \"PENDING\",\n    \"sub_status\": \"BANK_APPROVAL_PENDING\",\n    \"umrn\": \"ICIC70212102000056683\",\n    \"authFailureReason\": \"\",\n    \"start_date\": \"2022-06-23 23:43:39\",\n    \"source\": \"UI\",\n    \"end_date\": \"2024-06-23 23:59:59\",\n    \"auth_amount\": \"10\",\n    \"return_url\": \"https://google.com\",\n    \"show_nps_feedback\": false,\n    \"redirectOnCheckoutFailure\": true,\n    \"redirection_payload\": {\n      \"cf_subReferenceId\": 73967,\n      \"cf_subscriptionId\": \"test_checkout_001ywy\",\n      \"cf_authAmount\": 1,\n      \"cf_referenceId\": \"N/A\",\n      \"cf_status\": \"\",\n      \"cf_message\": \"\",\n      \"signature\": \"uUi1Elcktgn0st8yScO1J58gyMvTteEZ08bJaoqSwFI=\",\n      \"cf_umrn\": \"ICIC70212102000056683\",\n      \"cf_mode\": \"NPCI_SBC\",\n      \"cf_subscriptionPaymentId\": 98472,\n      \"cf_checkoutStatus\": \"\"\n    }\n  }\n}\n", "latency": 0, "statusCode": 200, "label": "Emandate Success", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "c615daa3-8f60-47e1-bec6-471fce7bc07c", "body": "{\n  \"status\": \"OK\",\n  \"message\": \"Get Subscription and auth Details\",\n  \"data\": {\n    \"subscription_id\": \"test_token_2kjw1dwdb1\",\n    \"sub_reference_id\": 73976,\n    \"selected_mode\": \"E_MANDATE\",\n    \"auth_status\": \"FAILED\",\n    \"sub_status\": \"INITIALIZED\",\n    \"authFailureReason\": \"Insufficient balance in account\",\n    \"start_date\": \"2022-06-23 23:50:28\",\n    \"source\": \"UI\",\n    \"end_date\": \"2024-06-23 23:59:59\",\n    \"return_url\": \"https://webhook.site/8f972515-186e-4e49-9296-22759b2a0271\",\n    \"show_nps_feedback\": false,\n    \"redirectOnCheckoutFailure\": false,\n    \"redirection_payload\": {\n      \"cf_subReferenceId\": 73976,\n      \"cf_subscriptionId\": \"test_token_2kjw1dwdb1\",\n      \"cf_authAmount\": 1,\n      \"cf_referenceId\": \"N/A\",\n      \"cf_status\": \"\",\n      \"cf_message\": \"\",\n      \"signature\": \"VPOkSMR32/l/mlImxls9oPtXndffIZJjusZuCC8iGTg=\",\n      \"cf_mode\": \"NPCI_SBC\",\n      \"cf_subscriptionPaymentId\": 98483,\n      \"cf_checkoutStatus\": \"\"\n    }\n  }\n}", "latency": 0, "statusCode": 200, "label": "Emandate Failure", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "7fcf7118-4683-4fbe-99be-d0c0772d7168", "body": "{\n  \"status\": \"OK\",\n  \"message\": \"Get Subscription and auth Details\",\n  \"data\": {\n    \"subscription_id\": \"test_token_2wdw\",\n    \"sub_reference_id\": 985,\n    \"selected_mode\": \"UPI\",\n    \"auth_status\": \"PENDING\",\n    \"sub_status\": \"INITIALISED\",\n    \"auth_amount\": 10,\n    \"source\": \"UI\",\n    \"umn\": \"ICIC70212102000056683\",\n    \"authFailureReason\": null,\n    \"start_date\": \"2022-06-01 01:55:35\",\n    \"end_date\": \"2024-05-19 23:59:59\",\n    \"return_url\": \"https://webhook.site/8f972515-186e-4e49-9296-22759b2a0271\",\n    \"redirectOnCheckoutFailure\": true,\n    \"redirection_payload\": {\n      \"cf_subReferenceId\": 985,\n      \"cf_subscriptionId\": \"\",\n      \"cf_authAmount\": 1,\n      \"cf_referenceId\": \"N/A\",\n      \"cf_status\": \"ACTIVE\",\n      \"cf_message\": \"Subscription authorised successfully\",\n      \"signature\": \"SjrHAcGXaL+s8S/ckegrz03NROslavrsLXmRsb+KhIc=\",\n      \"cf_umrn\": \"ICIC70212102000056683\",\n      \"cf_subscriptionPaymentId\": 1258,\n      \"cf_checkoutStatus\": \"SUCCESS\"\n    }\n  }\n}", "latency": 0, "statusCode": 200, "label": "UPI Pending", "headers": [{"key": "Access-Control-Expose-Headers", "value": "X-Request-ID"}, {"key": "X-Request-ID", "value": "vvvjkfadarfadace6a14b1000f6a5c3408d96dcb523"}], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "6385eadd-705e-420b-96bb-0e6197458bbc", "body": "{\n  \"status\": \"OK\",\n  \"message\": \"Get Subscription and auth Details\",\n  \"data\": {\n    \"subscription_id\": \"test_checkout_return_00031\",\n    \"sub_reference_id\": 70781,\n    \"selected_mode\": \"UPI\",\n    \"auth_status\": \"FAILED\",\n    \"authFailureReason\": \"User has rejected the transaction on pre-login page\",\n    \"start_date\": \"2022-05-17 11:09:55\",\n    \"end_date\": \"2024-05-17 23:59:59\",\n    \"redirectOnCheckoutFailure\": true,\n    \"return_url\": \"https://google.com\",\n    \"redirection_payload\": {\n      \"cf_subReferenceId\": 73967,\n      \"cf_subscriptionId\": \"test_checkout_001ywy\",\n      \"cf_authAmount\": 1,\n      \"cf_referenceId\": \"N/A\",\n      \"cf_status\": \"\",\n      \"cf_message\": \"\",\n      \"signature\": \"uUi1Elcktgn0st8yScO1J58gyMvTteEZ08bJaoqSwFI=\",\n      \"cf_umrn\": \"ICIC70212102000056683\",\n      \"cf_mode\": \"NPCI_SBC\",\n      \"cf_subscriptionPaymentId\": 98472,\n      \"cf_checkoutStatus\": \"\"\n    }\n  }\n}", "latency": 0, "statusCode": 200, "label": "UPI Failure", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "016ebf65-c199-4e25-80b1-d5d828a7a6d2", "body": "{\n  \"status\": 200,\n  \"message\": \"Subscription and Auth Details\",\n  \"data\": {\n    \"auth_amount\": 10.46,\n    \"auth_status\": \"INITIALIZED\",\n    \"authFailureReason\": \"\",\n    \"end_date\": \"2024-06-23 15:35:07\",\n    \"redirection_payload\": {\n      \"cf_authAmount\": 10.46,\n      \"cf_checkoutStatus\": \"\",\n      \"cf_message\": \"\",\n      \"cf_mode\": \"NPCI_SBC\",\n      \"cf_referenceId\": \"N/A\",\n      \"cf_status\": \"\",\n      \"cf_subReferenceId\": 73952,\n      \"cf_subscriptionId\": \"SBCCheckouttest9\",\n      \"cf_subscriptionPaymentId\": 99038,\n      \"signature\": \"ZkBEsKtSWPvqdHv24PzOskwthV7IE4J7Duz6DMGKIPU=\"\n    },\n    \"return_url\": \"google.com\",\n    \"selected_mode\": \"E_MANDATE\",\n    \"source\": \"API\",\n    \"start_date\": \"2022-07-05 11:07:55\",\n    \"sub_reference_id\": 73952,\n    \"sub_status\": \"INITIALIZED\",\n    \"subscription_id\": \"SBCCheckouttest9\"\n  }\n}", "latency": 0, "statusCode": 200, "label": "Initialised", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "d42e52da-36fa-4700-850a-2927466851c6", "body": "{\n  \"status\": \"OK\",\n  \"message\": \"Get Subscription and auth Details\",\n  \"data\": {\n    \"subscription_id\": \"test_checkout_001ywy\",\n    \"sub_reference_id\": 73967,\n    \"selected_mode\": \"E_MANDATE\",\n    \"auth_status\": \"PENDING\",\n    \"sub_status\": \"BANK_APPROVAL_PENDING\",\n    \"umrn\": \"ICIC70212102000056683\",\n    \"authFailureReason\": \"\",\n    \"start_date\": \"2022-06-23 23:43:39\",\n    \"source\": \"UI\",\n    \"end_date\": \"2024-06-23 23:59:59\",\n    \"auth_amount\": \"10\",\n    \"return_url\": \"google.com\",\n    \"show_nps_feedback\": true,\n    \"redirectOnCheckoutFailure\": false,\n    \"redirection_payload\": {\n      \"cf_subReferenceId\": 73967,\n      \"cf_subscriptionId\": \"test_checkout_001ywy\",\n      \"cf_authAmount\": 1,\n      \"cf_referenceId\": \"N/A\",\n      \"cf_status\": \"\",\n      \"cf_message\": \"\",\n      \"signature\": \"uUi1Elcktgn0st8yScO1J58gyMvTteEZ08bJaoqSwFI=\",\n      \"cf_umrn\": \"ICIC70212102000056683\",\n      \"cf_mode\": \"NPCI_SBC\",\n      \"cf_subscriptionPaymentId\": 98472,\n      \"cf_checkoutStatus\": \"\"\n    }\n  }\n}\n", "latency": 0, "statusCode": 200, "label": "Emandate SUCCESS (NPS)", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "crudKey": "id", "callbacks": []}], "responseMode": null, "type": "http"}, {"uuid": "00ad84b9-3af5-4efd-97ee-7fcadfe2c11c", "documentation": "", "method": "get", "endpoint": "auth-details/:authId", "responses": [{"uuid": "6dd4e07d-de06-4e00-a1f6-5293ab0fb26e", "body": "{\n  \"status\": 200,\n  \"message\": \"\",\n  \"data\": {\n    \"auth_status\": \"PENDING\"\n  }\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "82a34d1c-d951-4073-8b7e-7c0b8b27ebfb", "body": "{\n  \"status\": 200,\n  \"message\": \"Auth Details fetched successfully\",\n  \"data\": {\n    \"auth_status\": \"FAILED\"\n  }\n}", "latency": 0, "statusCode": 200, "label": "Failed", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "d4d799d3-4210-4146-b6c4-002669d300ed", "body": "{\n  \"status\": 200,\n  \"message\": \"Auth Details fetched successfully\",\n  \"data\": {\n    \"auth_status\": \"ACTIVE\"\n  }\n}", "latency": 0, "statusCode": 200, "label": "SUCCESS", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}], "responseMode": null, "type": "http"}, {"uuid": "41e86a55-b06e-49ec-83a7-cb28496a426f", "documentation": "", "method": "post", "endpoint": "validate-card", "responses": [{"uuid": "2a83e47f-e6ff-419a-a7de-2a170d92d6b7", "body": "{\n  \"status\": 200,\n  \"message\": \"Verify Card\",\n  \"data\": {\n    \"is_valid\": true,\n    \"validation_message\": \"success/failure message\"\n  }\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}], "responseMode": null, "type": "http"}, {"uuid": "623b7832-d297-4603-936c-71fe5354d864", "documentation": "", "method": "post", "endpoint": "simulator/:payment_type/:auth_id", "responses": [{"uuid": "3bcfe479-7954-46b5-8660-387622de3a42", "body": "{\n  \"status\": 200,\n  \"message\": \"Update Auth - Simulator\",\n  \"data\": {\n    \"auth_id\": 1369,\n    \"status\": \"ACTIVE\"\n  }\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}, {"uuid": "d7cb894e-f26f-42bc-8c82-d4dafe592a07", "body": "{}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "filePath": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "databucketID": "", "bodyType": "INLINE", "crudKey": "id", "callbacks": []}], "responseMode": null, "type": "http"}], "proxyMode": false, "proxyHost": "", "cors": true, "headers": [{"key": "Content-Type", "value": "application/json"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Origin, Accept, Authorization, Content-Length, X-Requested-With"}], "proxyReqHeaders": [{"key": "", "value": ""}], "proxyResHeaders": [{"key": "", "value": ""}], "proxyRemovePrefix": false, "hostname": "", "tlsOptions": {"enabled": false, "type": "CERT", "pfxPath": "", "certPath": "", "keyPath": "", "caPath": "", "passphrase": ""}, "data": [], "folders": [], "rootChildren": [{"type": "route", "uuid": "a31356c0-d2d6-11eb-a936-4b3842d7c4e9"}, {"type": "route", "uuid": "3c664340-ea20-11eb-bc47-dfb7587cf92c"}, {"type": "route", "uuid": "50c9d6f0-ef96-11eb-bc47-dfb7587cf92c"}, {"type": "route", "uuid": "2ebe5640-ef9a-11eb-bc47-dfb7587cf92c"}, {"type": "route", "uuid": "6c214d73-729b-45bf-85ca-ed8a2f02132c"}, {"type": "route", "uuid": "6a2acfeb-5263-41e7-a79a-e9b458123515"}, {"type": "route", "uuid": "dfe3a739-c169-4364-b697-316d316c3a56"}, {"type": "route", "uuid": "7d005410-a775-4ed3-b13b-a816a67956db"}, {"type": "route", "uuid": "c07f8749-1931-43c4-992e-bfb7218f6395"}, {"type": "route", "uuid": "77a80726-57e4-4689-83f5-abd61592fc96"}, {"type": "route", "uuid": "d72d807b-6af2-49f8-810e-c4e85d9bd82e"}, {"type": "route", "uuid": "00ad84b9-3af5-4efd-97ee-7fcadfe2c11c"}, {"type": "route", "uuid": "41e86a55-b06e-49ec-83a7-cb28496a426f"}, {"type": "route", "uuid": "623b7832-d297-4603-936c-71fe5354d864"}], "callbacks": []}