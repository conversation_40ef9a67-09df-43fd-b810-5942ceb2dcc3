<script>
	import { assets } from '$app/paths';
	import { track } from '$utils/amplitude';
	import {
		PAYMENT_MODES_TITLE,
		AUTH_STATUS,
		PAYMENT_MODES,
		SBC_PAYMENT_MODE
	} from '$utils/constants';
	let SuccessTick = `${assets}/images/Success_Tick.svg`;
	let OrangeExclamation = `${assets}/images/Pending_Exclamation.svg`;

	// Actions
	import { updateAmplitude } from '$store/actions/amplitude.svelte';

	// Constants
	import { PLAN_FREQUENCIES, PLAN_TYPES, CURRENCY, DATE_FORMATS } from '$utils/constants';
	// Helpers
	import { formatDate, formatExpiryDate } from '$utils/helpers';
	import { onMount } from 'svelte/internal';

	export let subscription;
	export let subscriptionAuthDetails;
	export let selectedMode;
	export let merchant;
	export let token;
	export let amplitudeEvent;

	let event =
		subscriptionAuthDetails.auth_status == AUTH_STATUS.ACTIVE
			? 'checkout_success'
			: subscriptionAuthDetails.auth_status == AUTH_STATUS.PENDING &&
			  selectedMode == PAYMENT_MODES[0]
			? 'checkout_success'
			: 'checkout_pending';

	// Function to check if a currency is international (non-INR)
	function isInternationalCurrency(currency) {
		// List of international currencies
		const internationalCurrencies = [
			'USD', 'EUR', 'GBP', 'AUD', 'CAD', 'SGD', 'HKD', 'JPY',
			'CHF', 'NZD', 'AED', 'SAR', 'QAR', 'KWD', 'BHD', 'OMR',
			'MYR', 'THB', 'PHP', 'ZAR', 'SEK', 'NOK', 'DKK', 'RUB'
		];

		return currency !== 'INR' && internationalCurrencies.includes(currency);
	}

	function EventCall() {
		track(
			event,
			{
				linkCreationSource: subscriptionAuthDetails.source,
				merchantType: merchant.merchant_meta.merchant_type,
				authID: '',
				...(selectedMode === PAYMENT_MODES[0]
					? {
							paymentMode: SBC_PAYMENT_MODE.SBC_NPCI,
							subscriptionMode: 'BANK ACCOUNT',
							transactionStatus: subscriptionAuthDetails.auth_status
					  }
					: selectedMode == PAYMENT_MODES[1]
					? {
							subscriptionMode: selectedMode,
							paymentMode: SBC_PAYMENT_MODE.SBC_UPI,
							transactionStatus: subscriptionAuthDetails.auth_status
					  }
					: {
							merchantType: merchant.merchant_meta.merchant_type,
							paymentMode: SBC_PAYMENT_MODE.SBC_CARD,
							subscriptionMode: selectedMode,
							transactionStatus: subscriptionAuthDetails.auth_status
					  })
			},
			amplitudeEvent,
			token
		);
	}
	onMount(() => {
		updateAmplitude(token).then(() => {
			EventCall();
		});
	});
	let Amount = subscription.type === PLAN_TYPES[0] ? 'Recurring Amount' : 'Max. Amount';
</script>

<div class="flex-col mt-4">
	<div class="flex justify-center status-icon">
		{#if subscriptionAuthDetails.auth_status == AUTH_STATUS.ACTIVE || (subscriptionAuthDetails.auth_status == AUTH_STATUS.PENDING && selectedMode == PAYMENT_MODES[0]) || (selectedMode == PAYMENT_MODES[2] && isInternationalCurrency(subscription.currency))}
			<img src={SuccessTick} alt="Success Tick" />
		{:else if subscriptionAuthDetails.auth_status == AUTH_STATUS.PENDING}
			<img src={OrangeExclamation} alt="Pending Exclamation" />
		{/if}
	</div>
	<div
		class="flex justify-center mt-6 md:mx-16 text-2xl px-8 md:p-0 font-semibold header text-center"
	>
		<!-- 
		e-Mandate Registraion Successful - Active (Pending doesn't exist)
		UPI Registraion Successful (Success)
		UPI Registration Success but debit pending (Pending)
		Authorization Successful - Pending & Active 
	-->
		{#if selectedMode === PAYMENT_MODES[2]}
			Authorization Successful
		{:else}
			{PAYMENT_MODES_TITLE[selectedMode]} Registration
			{subscriptionAuthDetails.auth_status == AUTH_STATUS.ACTIVE
				? 'Successful'
				: subscriptionAuthDetails.auth_status == AUTH_STATUS.PENDING &&
				  selectedMode == PAYMENT_MODES[0]
				? 'Successful'
				: 'Success but debit pending'}
		{/if}
	</div>
	{#if selectedMode === PAYMENT_MODES[0]}
		<!-- Do Nothing -->
	{:else if selectedMode === PAYMENT_MODES[1]}
		<div class="flex justify-center mt-6 md:mx-16 px-12 md:p-0 text-center">
			{subscriptionAuthDetails.auth_status == AUTH_STATUS.ACTIVE
				? subscriptionAuthDetails.auth_amount > 1
					? 'Immediate charges of'
					: 'Authorization amount of '
				: subscriptionAuthDetails.auth_status == AUTH_STATUS.PENDING
				? 'The debit of the immediate charge of '
				: ''}
			{!!subscription.currency ? CURRENCY[subscription.currency].symbol : subscription.currency}
			{' ' +
				new Intl.NumberFormat('en-IN', {
					minimumFractionDigits: 2,
					maximumFractionDigits: 2
				}).format(subscriptionAuthDetails.auth_amount) +
				' '}
			{subscriptionAuthDetails.auth_status == AUTH_STATUS.ACTIVE
				? 'debited successfully.'
				: subscriptionAuthDetails.auth_status == AUTH_STATUS.PENDING
				? 'is pending to be confirmed.'
				: ''}
		</div>
	{:else if selectedMode === PAYMENT_MODES[2]}
		<div class="flex justify-center mt-6 md:mx-16 px-8 md:p-0 text-center">
			You will receive a success confirmation via SMS within the next 3 hours.
		</div>
	{/if}
	<div
		class="grid justify-end grid-cols-[max-content_50%] gap-x-8 md:gap-x-16 gap-y-2 px-12 pt-6 pb-0 font-medium text-sm"
	>
		<div class="label text-textSecondary">Subscription ID</div>
		<div class="value break-all">{subscriptionAuthDetails.subscription_id}</div>
		{#if selectedMode === PAYMENT_MODES[0]}
			<div class="label text-textSecondary">UMRN</div>
			<div class="value break-all">{subscriptionAuthDetails.umrn}</div>
		{/if}
		{#if selectedMode === PAYMENT_MODES[1]}
			<div class="label text-textSecondary">UMN</div>
			<div class="value break-all">{subscriptionAuthDetails.umn}</div>
		{/if}
		<!-- <div class="label text-textSecondary">Plan Name</div> -->
		<!-- <div class="value break-all">{subscription.plan_name}</div> -->
		<div class="label text-textSecondary">Start & End Date</div>
		<div class="value">
			{formatDate(subscriptionAuthDetails.start_date, DATE_FORMATS.DD_MMM_YYYY)} - {formatExpiryDate(
				subscriptionAuthDetails.end_date
			)}
		</div>
		<div class="label text-textSecondary">Frequency</div>
		<div class="value">
			{#if subscription.type === PLAN_TYPES[0]}
				{subscription.intervals > 1
					? `${subscription.intervals} ${subscription.interval_type.toLowerCase()}s`
					: PLAN_FREQUENCIES[subscription.interval_type.toUpperCase()]}
			{:else}
				As and when presented
			{/if}
		</div>
		<div class="label text-textSecondary">{Amount}</div>
		<div class="value">
			{!!subscription.currency ? subscription.currency: subscription.currency}
			{' ' +
				new Intl.NumberFormat('en-IN', {
					minimumFractionDigits: 2,
					maximumFractionDigits: 2
				}).format(subscription.amount)}
		</div>

		{#if subscription.type == PLAN_TYPES[0] && selectedMode === PAYMENT_MODES[1]}
			<div class="label text-textSecondary">Next Charge Date</div>
			<div class="value">
				{formatDate(
					subscription.calculated_first_charge_date[subscriptionAuthDetails.selected_mode],
					DATE_FORMATS.DD_MMM_YYYY
				)}
			</div>
		{/if}
	</div>
</div>
