<script>
	// Constants
	import { DATE_FORMATS, PLAN_TYPES, CURRENCY, PAYMENT_MODES } from '$utils/constants';

	// Helpers
	import { formatDate, formatExpiryDate } from '$utils/helpers';

	// Props
	export let subscription;
	export let selectedMode;

	let textTitle = subscription.plan_name;
	let details = { ...subscription };
</script>

<div
	class="card-sub-header px-6 py-4 relative md:h-18 shadow-[0_1px_3px_rgba(0,0,0,0.2)] bg-primaryGrey text-base"
>
	<div class="grid md:grid-cols-2 gap-4">
		<div class="col flex flex-col">
			<!-- <div class="title flex items-center text-sm font-semibold">{textTitle || ''}</div> -->
			<div class="date text-xs pt-1">
				Start:
				<span class="font-semibold">
					{formatDate(subscription.start_date, DATE_FORMATS.DD_MMM_YYYY)}
				</span>
				| End:
				<span class="font-semibold">
					{formatExpiryDate(subscription.end_date)}
				</span>
			</div>
		</div>
		<div class="w-full border-t border-gray-300 md:hidden" />
		<div class="flex flex-col">
			{#if !!details.type}
				<div class="md:text-right  text-sm font-semibold">
					{#if details.type === PLAN_TYPES[1]}
						Max. Amount
					{/if}
					{!!details.currency ? details.currency : subscription.currency}
					{new Intl.NumberFormat('en-IN', {
						minimumFractionDigits: 2,
						maximumFractionDigits: 2
					}).format(details.amount)}
					{#if details.type === PLAN_TYPES[0]}
						every {details.intervals > 1
							? `${details.intervals} ${details.interval_type.toLowerCase()}s`
							: details.interval_type.toLowerCase()}
					{/if}
				</div>
				{#if subscription.type == PLAN_TYPES[0] && !!subscription.calculated_first_charge_date && !!subscription.calculated_first_charge_date[selectedMode]}
					<div class="md:text-right text-xs pt-1 font-semibold">
						First Charge Date: {formatDate(
							subscription.calculated_first_charge_date[selectedMode],
							DATE_FORMATS.DD_MMM_YYYY
						)}
					</div>
				{/if}
				{#if selectedMode != PAYMENT_MODES[0] && parseInt(subscription.authorization_amount, 10) > 1}
					<div class="md:text-right text-xs pt-1">
						(₹{new Intl.NumberFormat('en-IN', {
							minimumFractionDigits: 2,
							maximumFractionDigits: 2
						}).format(subscription.authorization_amount)} Immediate Charge)
					</div>
				{/if}
			{/if}
		</div>
	</div>
</div>
