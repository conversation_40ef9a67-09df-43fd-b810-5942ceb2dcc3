<script>
	import { UPI_APPS_CONFIG } from '$utils/constants';
	import ICON_CROSS from '$lib/Icons/Cross.svelte';
	import ICON_UPI from '$lib/Icons/upi.svelte';

	export let apps = [];
	export let handleAppSelect;
	export let handleClose;

	// Create a fallback app config for unknown apps
	const getFallbackAppConfig = (app) => {
		return {
			logo_url: null, // Will use generic UPI icon
			app_name: app.appName,
			id: app.packageId
		};
	};

	// Get app config (known or fallback)
	const getAppConfig = (app) => {
		if (app.isKnownApp && app.knownAppKey) {
			return UPI_APPS_CONFIG[app.knownAppKey];
		}
		return getFallbackAppConfig(app);
	};
</script>

<div
	class="modal-wrapper shadow-xl flex flex-col w-[90%] md:w-6/12 mx-auto mt-[15vh] md:mt-20 rounded-lg bg-white max-h-[70vh] overflow-hidden"
>
	<!-- Modal Header -->
	<div class="modal-header flex justify-between items-center p-4 border-b border-gray-200">
		<h3 class="text-lg font-semibold text-textPrimary">Choose UPI App</h3>
		<button class="w-6 h-6 flex items-center justify-center" on:click={handleClose}>
			<svelte:component this={ICON_CROSS} />
		</button>
	</div>

	<!-- Modal Content -->
	<div class="modal-content p-4 overflow-y-auto">
		{#if apps.length === 0}
			<div class="text-center py-8 text-textSecondary">
				<svelte:component this={ICON_UPI} />
				<p class="mt-2">No UPI apps found</p>
			</div>
		{:else}
			<div class="flex flex-row flex-wrap justify-start gap-2">
				{#each apps as app}
					{@const appConfig = getAppConfig(app)}
					<button
						class="p-2 text-center shadow-[0_2px_8px_rgba(125,68,240,0.12)] rounded-lg mb-6 hover:bg-productPrimaryHover w-18 h-16 cursor-pointer transition-colors duration-150 border-none bg-white"
						on:click={() => handleAppSelect(app)}
						aria-label={`Select ${appConfig.app_name} for UPI payment`}
					>
						<div class="Icon flex justify-center">
							<div
								class="border flex justify-center items-center align-center border-solid border-rgba(125, 68, 240, 0.1) border-radius rounded-lg w-8 h-8"
							>
								{#if appConfig.logo_url}
									<img
										src={appConfig.logo_url}
										alt={appConfig.app_name}
										class="image p-1 w-auto h-auto"
									/>
								{:else}
									<div class="w-full h-full flex items-center justify-center">
										<svelte:component this={ICON_UPI} />
									</div>
								{/if}
							</div>
						</div>
						<span class="name text-xxs text-center text-textPrimary font-medium leading-tight mt-1">
							{appConfig.app_name}
						</span>
					</button>
				{/each}
			</div>
		{/if}
	</div>

	<!-- Modal Footer -->
	<div class="modal-footer p-4 border-t border-gray-200 bg-gray-50">
		<p class="text-xs text-textSecondary text-center">
			Select any UPI app installed on your device to complete the payment
		</p>
	</div>
</div>

<style>
	.text-xxs {
		font-size: 0.625rem;
		line-height: 0.75rem;
	}
</style>
