<script>
	import { UPI_APPS_CONFIG } from '$utils/constants';
	import ICON_CROSS from '$lib/Icons/Cross.svelte';
	import ICON_UPI from '$lib/Icons/upi.svelte';

	export let apps = [];
	export let handleAppSelect;
	export let handleClose;

	// Create a fallback app config for unknown apps
	const getFallbackAppConfig = (app) => {
		return {
			logo_url: null, // Will use generic UPI icon
			app_name: app.appName,
			id: app.packageId
		};
	};

	// Get app config (known or fallback)
	const getAppConfig = (app) => {
		if (app.isKnownApp && app.knownAppKey) {
			return UPI_APPS_CONFIG[app.knownAppKey];
		}
		return getFallbackAppConfig(app);
	};
</script>

<div
	class="modal-wrapper shadow-xl flex flex-col w-[90%] md:w-6/12 mx-auto mt-[15vh] md:mt-20 rounded-lg bg-white max-h-[70vh] overflow-hidden"
>
	<!-- Modal Header -->
	<div class="modal-header flex justify-between items-center p-4 border-b border-gray-200">
		<h3 class="text-lg font-semibold text-textPrimary">Choose UPI App</h3>
		<button class="w-6 h-6 flex items-center justify-center" on:click={handleClose}>
			<svelte:component this={ICON_CROSS} />
		</button>
	</div>

	<!-- Modal Content -->
	<div class="modal-content p-4 overflow-y-auto">
		{#if apps.length === 0}
			<div class="text-center py-8 text-textSecondary">
				<svelte:component this={ICON_UPI} />
				<p class="mt-2">No UPI apps found</p>
			</div>
		{:else}
			<div class="grid grid-cols-3 md:grid-cols-4 gap-4">
				{#each apps as app}
					{@const appConfig = getAppConfig(app)}
					<div
						class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors duration-150"
						on:click={() => handleAppSelect(app)}
					>
						<div class="w-12 h-12 mb-2 flex items-center justify-center">
							{#if appConfig.logo_url}
								<img
									src={appConfig.logo_url}
									alt={appConfig.app_name}
									class="w-10 h-10 rounded-lg"
									on:error={(e) => {
										// Fallback to generic UPI icon if image fails to load
										e.target.style.display = 'none';
										e.target.nextElementSibling.style.display = 'block';
									}}
								/>
								<div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center" style="display: none;">
									<svelte:component this={ICON_UPI} />
								</div>
							{:else}
								<div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
									<svelte:component this={ICON_UPI} />
								</div>
							{/if}
						</div>
						<span class="text-xs text-center text-textPrimary font-medium leading-tight">
							{appConfig.app_name}
						</span>
						{#if !app.isKnownApp}
							<span class="text-xxs text-textSecondary mt-1">Third-party</span>
						{/if}
					</div>
				{/each}
			</div>
		{/if}
	</div>

	<!-- Modal Footer -->
	<div class="modal-footer p-4 border-t border-gray-200 bg-gray-50">
		<p class="text-xs text-textSecondary text-center">
			Select any UPI app installed on your device to complete the payment
		</p>
	</div>
</div>

<style>
	.text-xxs {
		font-size: 0.625rem;
		line-height: 0.75rem;
	}
</style>
