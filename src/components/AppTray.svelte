<script>
	import { UPI_APPS_CONFIG } from '$utils/constants';
	import ICON_UPI from '$lib/Icons/upi.svelte';

	export let app;

	// Get app config - handle both legacy string apps and discovered app objects
	$: appConfig = (() => {
		if (typeof app === 'string') {
			// Legacy app string (e.g., 'PHONEPE', 'GPAY')
			return UPI_APPS_CONFIG[app];
		} else if (app && typeof app === 'object') {
			// Discovered app object
			if (app.isKnownApp && app.knownAppKey) {
				return UPI_APPS_CONFIG[app.knownAppKey];
			} else {
				// Unknown app - create fallback config
				return {
					logo_url: null,
					app_name: app.appName || 'UPI App'
				};
			}
		}
		return null;
	})();
</script>

{#if appConfig}
	<div class="Icon flex justify-center">
		<div
			class="border flex justify-center items-center align-center border-solid border-rgba(125, 68, 240, 0.1) border-radius rounded-lg w-8 h-8"
		>
			{#if appConfig.logo_url}
				<img
					src={appConfig.logo_url}
					alt={appConfig.app_name}
					class="image p-1 w-auto h-auto"
					on:error={(e) => {
						// Fallback to generic UPI icon if image fails to load
						e.target.style.display = 'none';
						e.target.nextElementSibling.style.display = 'flex';
					}}
				/>
				<div class="w-full h-full flex items-center justify-center" style="display: none;">
					<svelte:component this={ICON_UPI} />
				</div>
			{:else}
				<div class="w-full h-full flex items-center justify-center">
					<svelte:component this={ICON_UPI} />
				</div>
			{/if}
		</div>
	</div>
	<span class="name text-xxs">{appConfig.app_name}</span>
{/if}

<style>
	.text-xxs {
		font-size: 0.625rem;
		line-height: 0.75rem;
	}
</style>
