// Icons
import ICON_UNION from '$lib/Icons/union.svelte';
import ICON_UPI from '$lib/Icons/upi.svelte';
import ICON_CARD from '$lib/Icons/card.svelte';

export enum PAGES {
	'FORM',
	'SUMMARY',
	'TIMER',
	'STATUS'
}

export enum AUTH_STATUS {
	'INITIALIZED' = 'INITIALIZED',
	'CREATED' = 'CREATED',
	'ACTIVE' = 'ACTIVE',
	'PENDING' = 'PENDING',
	'FAILED' = 'FAILED'
}

export enum SBC_PAYMENT_MODE {
	'SBC_NPCI' = 'SBC_NPCI',
	'SBC_UPI' = 'SBC_UPI',
	'SBC_CARD' = 'SBC_CARD'
}

export enum DATE_FORMATS {
	'DD_MMM_YYYY' = 'DD MMM YYYY'
}

export const PLAN_TYPES = ['PERIODIC', 'ON_DEMAND'];
export const PLAN_INTERVAL_TYPES = ['DAY', 'WEEK', 'MONTH', 'YEAR'];
export const PLAN_FREQUENCIES = {
	[PLAN_INTERVAL_TYPES[0]]: 'Daily',
	[PLAN_INTERVAL_TYPES[1]]: 'Weekly',
	[PLAN_INTERVAL_TYPES[2]]: 'Monthly',
	[PLAN_INTERVAL_TYPES[3]]: 'Yearly'
};

export const PAYMENT_MODES = ['E_MANDATE', 'UPI', 'CARD'];
export const PAYMENT_MODES_TITLE = {
	[PAYMENT_MODES[0]]: 'e-Mandate',
	[PAYMENT_MODES[1]]: 'UPI',
	[PAYMENT_MODES[2]]: 'Card'
};

export const PAYMENT_MODES_CONFIG = {
	[PAYMENT_MODES[0]]: {
		text: 'Bank Account',
		icon: ICON_UNION,
		alt: 'Bank Account',
		isVisible: false,
		account_type: [],
		corporate_name: '',
		utility_code: '',
		frequent_banks: [],
		is_authorized: false,
		all_banks: [
			{
				bank_id: '',
				bank_name: '',
				bank_display_name: '',
				auth: []
			}
		]
	},
	[PAYMENT_MODES[1]]: {
		text: 'UPI',
		icon: ICON_UPI,
		alt: 'UPI',
		available_handles: [],
		handles: [],
		isVisible: false,
		validate_UPI: true,
		isCollectSupported: true,
		isQrSupported: true,
		isIntentSupported: true,
		intentSupport: [],
		qrSupport: []
	},
	[PAYMENT_MODES[2]]: {
		text: 'Card',
		icon: ICON_CARD,
		alt: 'Card',
		isVisible: false,
		allowed_card_types: []
	}
};

export const REDIRECTION_MODES = {
	MANDATE_AUTHORIZATION: 'mandateAuthorization',
	MANDATE_AUTHORIZATION_CALLBACK: 'mandateAuthorizationCallback',
	MANDATE_REDIRECT: 'mandateRedirect'
};

export enum SOURCE_MODES {
	// NON SEAMLESS
	UI = 'UI',
	API = 'API',
	UI_BULK = 'UI_BULK',
	API_NON_SEAMLESS = 'API_NON_SEAMLESS',
	// SEAMLESS
	API_SEAMLESS = 'API_SEAMLESS'
}

// Currency
export const CURRENCY = {
	INR: { symbol: '₹' },
	BHD: { symbol: '.د.ب' },   // Bahraini Dinar
	PHP: { symbol: '₱' },
	NOK: { symbol: 'kr' },
	KWD: { symbol: 'د.ك' },   // Kuwaiti Dinar
	DKK: { symbol: 'kr' },
	KES: { symbol: 'KSh' },
	MUR: { symbol: '₨' },     // Mauritian Rupee
	ILS: { symbol: '₪' },
	GBP: { symbol: '£' },
	CNY: { symbol: '¥' },
	VND: { symbol: '₫' },
	CHF: { symbol: 'CHF' },
	ZAR: { symbol: 'R' },
	SEK: { symbol: 'kr' },
	CAD: { symbol: 'CA$' },
	AUD: { symbol: 'A$' },
	USD: { symbol: '$' },
	THB: { symbol: '฿' },
	MYR: { symbol: 'RM' },
	SGD: { symbol: 'S$' },
	OMR: { symbol: 'ر.ع.' },  // Omani Rial
	NPR: { symbol: '₨' },     // Nepalese Rupee
	NZD: { symbol: 'NZ$' },
	LKR: { symbol: '₨' },     // Sri Lankan Rupee
	AED: { symbol: 'د.إ' },   // UAE Dirham
	BDT: { symbol: '৳' },
	EUR: { symbol: '€' },
	QAR: { symbol: 'ر.ق' },   // Qatari Riyal
	HKD: { symbol: 'HK$' },
	JPY: { symbol: '¥' },
	SAR: { symbol: '﷼' },    // Saudi Riyal
	RUB: { symbol: '₽' }
};
  

// Accounts
export const ACCOUNT_TYPES = ['SAVINGS', 'CURRENT'];
export const ACCOUNT_TYPES_LABELS = {
	[ACCOUNT_TYPES[0]]: 'Savings',
	[ACCOUNT_TYPES[1]]: 'Current'
};
export const ACCOUNT_AUTHENTICATION_TYPE = ['DEBIT_CARD', 'NET_BANKING', 'AADHAAR'];
export const ACCOUNT_AUTHENTICATION_TYPES_LABELS = {
	[ACCOUNT_AUTHENTICATION_TYPE[0]]: 'Debit Card',
	[ACCOUNT_AUTHENTICATION_TYPE[1]]: 'Net Banking',
	[ACCOUNT_AUTHENTICATION_TYPE[2]]: 'Aadhaar'
};

// UPI
export const UPI_AMOUNT_LIMIT_PERIODIC = 5_000;
export const UPI_AMOUNT_LIMIT = 100_000;
export enum UPI_FLOWS {
	QR = 'QR',
	COLLECT = 'COLLECT',
	INTENT = 'INTENT'
}
export enum UPI_AUTH_FLOW {
	QR = 'UPI_QR_MANDATE',
	COLLECT = 'UPI_COLLECT',
	INTENT = 'UPI_INTENT'
}

export enum OS {
	IOS = 'iOS',
	ANDROID = 'Android'
}

export const UPI_APPS = ['PHONEPE', 'GPAY', 'PAYTM', 'BHIM', 'AMAZONPAY'];
export const UPI_APPS_CONFIG = {
	[UPI_APPS[0]]: {
		logo_url: 'https://cashfreelogo.cashfree.com/checkout/PhonePe.png',
		app_name: 'PhonePe',
		deeplink: 'phonepe',
		id: 'com.phonepe.app',
		ios: 'phonepe://',
		iosId: 'phonepe://'
	},
	[UPI_APPS[1]]: {
		logo_url: 'https://cashfreelogo.cashfree.com/checkout/GooglePay.png',
		app_name: 'Google Pay',
		deeplink: 'gpay://upi/mandate?',
		id: 'com.google.android.apps.nbu.paisa.user',
		ios: 'tez://upi/mandate?',
		iosId: 'tez://'
	},
	[UPI_APPS[2]]: {
		logo_url: 'https://cashfreelogo.cashfree.com/checkout/PAYTM.png',
		app_name: 'Paytm',
		deeplink: 'paytmmp',
		id: 'net.one97.paytm',
		ios: 'paytmmp://mandate?',
		iosId: 'paytmmp://'
	},
	[UPI_APPS[3]]: {
		logo_url: 'https://cashfreelogo.cashfree.com/checkout/BHIM.png',
		app_name: 'BHIM',
		deeplink: 'bhim',
		id: 'in.org.npci.upiapp',
		ios: 'bhim://upi://mandate?',
		iosId: 'bhim://'
	},
	[UPI_APPS[4]]: {
		logo_url: 'https://cashfreelogo.cashfree.com/checkout/AmazonPay.png',
		app_name: 'Amazon Pay',
		deeplink: 'amzn',
		id: 'in.amazon.mShop.android.shopping',
		ios: 'amzn://upi/mandate?',
		iosId: 'amazonpay://'
	}
};

// CARD
export const CARD_TYPES = ['VISA', 'MASTERCARD', 'RUPAY', 'MAESTRO', 'PAYPAL'];
export const SUPPORTED_CARD_TYPES = [CARD_TYPES[0], CARD_TYPES[1]];
export const CARD_MODES = ['CREDIT_CARD', 'DEBIT_CARD'];
export const CARD_MODES_CONFIG = {
	[CARD_MODES[0]]: {
		label: 'Credit'
	},
	[CARD_MODES[1]]: {
		label: 'Debit'
	}
};

export const UNTIL_CANCELLED_DATES = ['2099-12-31 00:00:00', '2050-12-31 00:00:00'];

export const PROD_TEST_MERCHANT_IDS = ['40624', '1848', '603'];

export const DOWNTIME = [];
