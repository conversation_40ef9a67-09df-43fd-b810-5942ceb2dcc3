import Bowser from 'bowser';
import creditCardType from 'credit-card-type';
import dayjs from 'dayjs';
import Regex from './regex';

import {
	DATE_FORMATS,
	OS,
	SUPPORTED_CARD_TYPES,
	UNTIL_CANCELLED_DATES,
	UPI_APPS,
	UPI_APPS_CONFIG
} from './constants';
import { postJSON } from './http';

declare global {
	interface Window {
		Android: any;
	}
}

/* eslint-disable-next-line */
String.prototype.toProperCase = function () {
	return this.replace(/\w\S*/g, function (txt) {
		return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
	});
};

export const camelToSnakeCase = (str) =>
	str.replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`);

export const formatDate = (str: string, format = DATE_FORMATS.DD_MMM_YYYY): string =>
	dayjs(str).format(format);
export const formatTime = (seconds: number): string => {
	let min: number | string = Math.floor(seconds / 60);
	min = min > 9 ? min : `0${min}`;
	let sec: string | number = seconds % 60;
	sec = sec > 9 ? sec : `0${sec}`;

	const time = `${min}:${sec}`;

	return time;
};
export const formatAccountNumber = (accNumber, isMasked = false) => {
	let last4Digits;
	if (isMasked) {
		last4Digits = accNumber.slice(-4);
		return `XXX...` + last4Digits;
	}

	return accNumber;
};
export const clearFormatCardNumber = (cardNumber) => {
	return cardNumber.replace(/[^0-9]/gi, '');
};
export const formatCardNumber = (cardNumber: string, card, isMasked = false): string => {
	const { getTypeInfo } = creditCardType;
	cardNumber = clearFormatCardNumber(cardNumber);
	let gap = [4, 8, 12];

	if (card) {
		const cardInfo = getTypeInfo(card.type);
		gap = cardInfo.gaps;
	}
	const offsets = [].concat(0, gap, cardNumber.length);
	let components = [];

	for (let i = 0; offsets[i] < cardNumber.length; i++) {
		const start = offsets[i];
		const end = Math.min(offsets[i + 1], cardNumber.length);
		components.push(cardNumber.substring(start, end));
	}
	if (isMasked) {
		const lastDigits = components.pop();
		components = components.map((v) => v.replaceAll(/[0-9]/g, 'X'));
		components.push(lastDigits);
	}
	return components.join(' ');
};

export const formatCardExpiresOn = (expiresOn: string): string => {
	expiresOn = expiresOn.split('/').join('');
	const offsets = [0, 2, expiresOn.length];
	const components = [];
	for (let i = 0; offsets[i] < expiresOn.length; i++) {
		const start = offsets[i];
		const end = Math.min(offsets[i + 1], expiresOn.length);
		components.push(expiresOn.substring(start, end));
	}
	return components.join('/');
};

export const replaceRestParams = (str: string, obj: any): string => {
	for (const key in obj) {
		str = str.replace(`{${key}}`, obj[key]);
	}

	return str;
};

export const hexToRGB = (hex: string): { r: number; g: number; b: number } | null => {
	// Expand shorthand form (e.g. "03F") to full form (e.g. "0033FF")
	const shorthandRegex = /^#?([a-f\d])([a-f\d])([a-f\d])$/i;
	hex = hex.replace(shorthandRegex, function (m, r, g, b) {
		return r + r + g + g + b + b;
	});

	const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
	return result
		? {
				r: parseInt(result[1], 16),
				g: parseInt(result[2], 16),
				b: parseInt(result[3], 16)
		  }
		: null;
};

export const isSupportedCard = (cardNumber: string): boolean => {
	const cardType = creditCardType(cardNumber)[0] ? creditCardType(cardNumber)[0]['type'] : null;
	if (SUPPORTED_CARD_TYPES.includes(cardType.toUpperCase())) return true;
	return false;
};

export const isCardExpired = (expiresOn: string): boolean => {
	const [month, year] = [expiresOn.slice(0, 2), expiresOn.slice(2, 4)];
	const expiresDate = dayjs(new Date(2000 + parseInt(year, 10), parseInt(month, 10) - 1, 1));
	const currentDate = dayjs();
	return currentDate < expiresDate;
};

export const isCardNumberValid = (cardNumber: string): boolean => {
	cardNumber = clearFormatCardNumber(cardNumber).match(/\d{16}/g);
	return !!cardNumber && !!cardNumber[0];
};

export const isSupportedUPI = (upiID, handles): boolean => {
	let supportedVPA = false;

	upiID = upiID.split('@');

	if (
		upiID.length == 2 &&
		new RegExp('^[a-zA-Z0-9_\\-\\.]+$').test(upiID[0]) &&
		handles.includes(upiID[1].toLowerCase())
	) {
		supportedVPA = true;
	}
	return supportedVPA;
};

export const getCursorPosition = (input) => {
	let position = 0;
	if (input.selectionStart || input.selectionStart === '0') {
		position = input.selectionStart;
	}
	return position;
};

export const setCursorPosition = (input, pos) => {
	if (!(input && input.setSelectionRange)) {
		return;
	}
	input.setSelectionRange(pos, pos);
};

export const removeKeyFromQueryParams = (queryParams, keyToRemove) => {
 if(!queryParams) return ''
	const params = {};
	queryParams.split('&').forEach((param) => {
		const [key, value] = param.split('=');
		if (key !== keyToRemove) {
			params[key] = value;
		}
	});
	return Object.entries(params)
		.map(([key, value]) => `${key}=${value}`)
		.join('&');
};

export const redirectionForAndroid = (App, qrData) => {
	const splittedURL = qrData.split('://');
	const upiPayString = splittedURL[1];
	const upiPayQuery = qrData.split('?')[1];
	let intentUrl = '';
	if (App === UPI_APPS[0]) {
		intentUrl = `${UPI_APPS_CONFIG[App].deeplink}://mandate?${removeKeyFromQueryParams(
			upiPayQuery,
			'sign'
		)}`;
	} else if (App === UPI_APPS[1]) {
		intentUrl = UPI_APPS_CONFIG[App].deeplink + removeKeyFromQueryParams(upiPayQuery, 'sign');
	} else if (App === UPI_APPS[2]) {
		intentUrl = `${UPI_APPS_CONFIG[App].deeplink}://mandate?${upiPayQuery}`;
	} else {
		intentUrl = 'intent://' + upiPayString;
	}
	intentUrl =
		intentUrl +
		'#Intent;scheme=upi;package=' +
		(UPI_APPS_CONFIG[App] ? UPI_APPS_CONFIG[App].id : '') +
		';end';
	if (App === 'Others') intentUrl = 'upi://' + upiPayString;
    console.log("test_paytm"+intentUrl);
	return intentUrl;
};

export const redirectionForIOS = (App, qrData) => {
	const upiPayQuery = qrData.split('?')[1];
	let intentUrl = '';

	if (App === UPI_APPS[0]) {
		intentUrl = `${UPI_APPS_CONFIG[App].ios}mandate?${removeKeyFromQueryParams(
			upiPayQuery,
			'sign'
		)}`;
	} else {
    intentUrl = UPI_APPS_CONFIG[App].ios + removeKeyFromQueryParams(upiPayQuery, 'sign');
  }
  console.log("intent_URL: "+intentUrl);
	return intentUrl;
};

export const isUpiVisible = (
	isCollectSupported,
	isIntentSupported,
	isQrSupported,
	intentSupport,
	qrSupport,
	os
) => {
	if (isCollectSupported) return true;

	const upiIntent = intentSupport.find(
		(osApp) => osApp.operatingSystem.toUpperCase() === os.toUpperCase()
	);
	const upiApps = upiIntent ? upiIntent.applications : [];

	if (os === OS.ANDROID || os === OS.IOS) {
		return upiApps.length > 0 && isIntentSupported;
	}

	return isQrSupported && qrSupport.length > 0;
};

export const getBowserData = (userAgent) => {
	const browserData = Bowser.parse(userAgent);

	const browserName = browserData.browser.name ? browserData.browser.name : 'NoBrowser';

	let osName = browserData.os.name ? browserData.os.name : 'NoOS';
	osName = `${browserName}-${osName}`;

	let browserVersion = browserData.browser.version ? browserData.browser.version : '0.0.1';

	browserVersion = browserVersion.split('.')[0];
	return {
		os_name: osName,
		os_version: browserVersion,
		platform: browserData.platform.type ? browserData.platform.type : 'NoPlatform',
		device_brand: browserData.platform.vendor ? browserData.platform.vendor : 'NA',
		device_model: browserData.platform.model ? browserData.platform.model : 'NA',
		os: osName.split('-')[1]
	};
};

export const detectSdk = (sdk: string): boolean => {
    if (typeof window === 'undefined') {
        return false;
    }

    if (
        window.webkit &&
        window.webkit.messageHandlers &&
        window.webkit.messageHandlers.nativeProcess
    ) {
        return sdk === 'ios';
    } else if (window.Android && window.Android.getAppList && window.Android.openApp) {
        return sdk === 'android';
    } else {
        return false;
    }
};

export const getAppListFromSDK = (onSdk: boolean, allowedUpiApps: string[]) => {
	if (onSdk) {
		let appList = window.Android.getAppList('upi://pay', 'upi://mandate');
		if (appList) {
			try {
				appList = JSON.parse(appList).map(function (app) {
					return app.appPackage;
				});
			} catch (e) {
				return [];
			}
		}
		console.log("test log")

		return allowedUpiApps.filter((app) => appList.includes(UPI_APPS_CONFIG[app].id));
	} else {
		return allowedUpiApps;
	}
};

export const getIOSAppListFromSDK = async (onSdk: boolean, allowedUpiApps: string[]): Promise<string[]> => {
	if (!onSdk) return allowedUpiApps;

	return new Promise((resolve) => {
		window.webkit.messageHandlers.nativeProcess.postMessage('getAppList');

		window.receiveAppList = (nativeAppList: any[]) => {
			try {
				const appPackages = nativeAppList.map((app) => app.appPackage || app.id);
				const filteredApps = allowedUpiApps.filter((app) =>
					appPackages.includes(UPI_APPS_CONFIG[app].iosId)
				);
				resolve(filteredApps);
			} catch (e) {
				resolve([]);
			}
		};
	});
};

export const formatExpiryDate = (subscriptionExpiryDate: string): string => {
	if (UNTIL_CANCELLED_DATES.includes(subscriptionExpiryDate)) {
		return 'Until Cancelled';
	}

	return formatDate(subscriptionExpiryDate, DATE_FORMATS.DD_MMM_YYYY);
};

export const customLogs = (message, type, merchantId) => {
	try {
		if ([316466, 40624].includes(merchantId)) {
			postJSON(
				'https://subscription-checkout-logs.serveo.net/log',
				null,
				{
					type: type,
					message: message,
				},
				{ type }
			);
		}
	} catch (error) {
		console.log('Analytics error', error)
	}
} 

export const emptyErrorMessage = 'This field cannot be blank.';

export const cardNumberValidation = (value: string, optional = false) => {
    if (!value) {
        if (!optional) {
            return emptyErrorMessage;
        }

        return null;
    }

    if (!Regex.card(value.trim())) {
        return 'Enter valid card number';
    }

    // Luhn algorithm check
    let nCheck = 0,
        bEven = false;

    for (let n = value.length - 1; n >= 0; n--) {
        // eslint-disable-next-line prefer-const
        let cDigit = value.charAt(n),
            nDigit = parseInt(cDigit, 10);

        if (bEven && (nDigit *= 2) > 9) nDigit -= 9;

        nCheck += nDigit;
        bEven = !bEven;
    }

    if (nCheck % 10 !== 0) {
        return 'Enter valid card number';
    }

    return null;
};