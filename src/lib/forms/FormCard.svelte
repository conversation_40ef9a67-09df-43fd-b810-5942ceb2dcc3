<script>
	import { assets } from '$app/paths';
	import { writable } from 'svelte/store';
	import { createForm } from 'svelte-forms-lib';
	import creditCardType from 'credit-card-type';
	import * as yup from 'yup';
	import Tooltip from '$components/Tooltip.svelte';

	// Components
	import Loader from '$components/Loader.svelte';
	import NavigateBack from '$components/NavigateBack.svelte';
	import PoweredBy from '$components/PoweredBy.svelte';

	// Constants
	import {
		CARD_TYPES,
		PAYMENT_MODES,
		PAYMENT_MODES_CONFIG,
		SBC_PAYMENT_MODE
	} from '$utils/constants';

	// Helpers
	import {
		isSupportedCard,
		isCardExpired,
		isCardNumberValid,
		formatCardNumber,
		formatCardExpiresOn,
		clearFormatCardNumber,
		getCursorPosition,
		setCursorPosition,
		cardNumberValidation,
	} from '$utils/helpers';

	// Icons
	let VISA = `${assets}/logos/VISA.png`;
	let MASTERCARD = `${assets}/logos/Mastercard.png`;
	let RUPAY = `${assets}/logos/Rupay.png`;
	let MAESTRO = `${assets}/logos/Maestro.png`;
	let PAYPAL = `${assets}/logos/Paypal.png`;
	let INFO_ICON = `${assets}/images/info-circle.svg`;

	import ICON_AUTHORIZE_TICK from '$lib/Icons/authorize_tick.svelte';
	import { track } from '$utils/amplitude';

	export let token;
	export let subscription;
	export let paymentOptions = PAYMENT_MODES_CONFIG;
	export let formState;
	export let merchant;
	export let verifyAuthAttempt;
	export let handleCardSubmit;
	export let resetMode;
	export let availableModes;
	export let amplitudeEvent;
    export let getCardTypeAndBankName;

	let cardConfig = paymentOptions[PAYMENT_MODES[2]];

	// Don't use fat arrow functions as we make use of this keyword
	yup.addMethod(yup.string, 'isCardExpired', function (message) {
		return this.test('is_card_expired', message, function (val) {
			return isCardExpired(val);
		});
	});

	yup.addMethod(yup.string, 'isCardNumberValid', function (message) {
		return this.test('is_card_number_valid', message, function (val) {
			return isCardNumberValid(val);
		});
	});

	creditCardType.addCard({
		niceType: 'Rupay',
		type: 'rupay',
		// @ts-ignore
		patterns: [
			[508500, 508999],
			[606985, 607384],
			[607385, 607484],
			[607485, 607984],
			[608001, 608100],
			[608101, 608200],
			[608201, 608300],
			[608301, 608350],
			[608351, 608500],
			[652150, 652849],
			[652850, 653049],
			[653050, 653149],
			[353600, 353810],
			[817200, 820199]
		],
		gaps: [4, 8, 12],
		lengths: [16],
		code: {
			name: 'CVV',
			size: 3
		}
	});

	const {
		// observables
		form,
		errors,
		touched,
		isValid,
		isSubmitting,
		// handlers
		handleChange,
		handleSubmit
	} = createForm({
		initialValues: {
			...formState,
			card_number: formState.card_number || '',
			card_number_cursor: formState.card_number_cursor || 0,
			card_holder_name: formState.card_holder_name || '',
			card_expires_on: formState.card_expires_on || '',
			card_cvv: formState.card_cvv || '',
			is_authorized: formState.is_authorized || false,
			card_network: formState.card_network || 'OTHER',
			verification_status: formState.verification_status || 'SUCCESS',
			card_type: formState.card_type || '',
			bank_name: formState.bank_name || '',
		},
		validationSchema: yup.object().shape({
		   card_number: yup
			.string()
			.required('Card number is required')
			.test(
				'is-valid-card',
				'Only Visa and Master cards are supported',
				(value) => {
				const digitsOnly = value?.replace(/\D/g, '') || '';
				if (digitsOnly.length <= 8) return true;

				const cardType = creditCardType(digitsOnly)[0];
				const validSchemes = ['visa', 'mastercard'];

				return validSchemes.includes(cardType?.type?.toLowerCase());
				}
			)
			.test(
				'is-valid-format',
				'Enter valid card number',
				(value) => {
				const digitsOnly = value?.replace(/\D/g, '') || '';
				if (digitsOnly.length <= 8) return true;
				return cardNumberValidation(digitsOnly) === null;
				}
			),
			card_holder_name: yup
				.string()
				.matches(/^[0-9a-zA-Z]+[0-9a-zA-Z ]*$/, 'Invalid Card Holder Name'),
			card_expires_on: yup
				.string()
				.matches(/^(0[1-9]|1[0-2])\/?[0-9]{2}$/, 'Invalid Expires On')
				.isCardExpired('Invalid Expires On'),
			card_cvv: yup.string().matches(/^[0-9]{3}$/, 'Invalid CVV'),
			is_authorized: yup.boolean().oneOf([true], 'This field must be checked')
		}),
		onSubmit: async (f) => {
			if (creditCardType($form.card_number)[0]['type'] == CARD_TYPES[0].toLowerCase()) {
				$form.card_network = 'VISA';
			} else if (creditCardType($form.card_number)[0]['type'] == CARD_TYPES[1].toLowerCase()) {
				$form.card_network = 'MASTERCARD';
			} else {
				$form.card_network = 'OTHERS';
			}

			if (isSupportedCard(f.card_number)) {
				const { err, res } = await verifyAuthAttempt(
					{
						account_number: clearFormatCardNumber(f.card_number),
						expires_on: f.card_expires_on
					},
					PAYMENT_MODES[2],
					token
				);
				$form.verification_status = !err ? 'SUCCESS' : `'FAILURE': ${res.data.validation_message}`;
				track(
					'CHECKOUT_VERIFICATION',
					{
						linkCreationSource: subscription.source,
						merchantType: merchant.merchant_meta.merchant_type,
						authID: '',
						verificationStatus: !err ? 'SUCCESS' : `'FAILURE': ${res.data.validation_message}`,
						paymentMode: SBC_PAYMENT_MODE.SBC_CARD,
						subscriptionMode: PAYMENT_MODES[2],
						cardNetwork: $form.card_network
					},
					amplitudeEvent,
					token
				);
				if (!!err || !res.data.is_valid) {
					$errors.card_number = res.data.validation_message;
				} else {
					handleCardSubmit({
						...f,
						card_number: clearFormatCardNumber(f.card_number)
					});
				}
			} else {
				$errors.card_number = 'Only Visa and Master cards are supported';
			}
			track(
				'CHECKOUT_FORM_SUBMIT',
				{
					linkCreationSource: subscription.source,
					merchantType: merchant.merchant_meta.merchant_type,
					authID: '',
					submissionMessage: $form.verification_status,
					paymentMode: SBC_PAYMENT_MODE.SBC_CARD,
					subscriptionMode: PAYMENT_MODES[2],
					cardNetwork: $form.card_network
				},
				amplitudeEvent,
				token
			);
		}
	});

	// Added as bug in svelte-form-lib Initial value of $isValid is true
	const formFilled = () => {
		return !(
			$form.is_authorized === false ||
			$form.card_number === '' ||
			$form.card_holder_name === '' ||
			$form.card_expires_on === '' ||
			$form.card_cvv === ''
		);
	};

	const pageState = writable({
		formFilled: formFilled()
	});

	// Function to handle card type and bank name API call
	const handleCardTypeBankNameAPICall = async (cardNumber) => {
		if (!cardNumber || cardNumber.length < 8) return;

		const digitsOnly = clearFormatCardNumber(cardNumber);

		try {
			const { res } = await getCardTypeAndBankName(digitsOnly, token);

			if(res && res.data && res.data.is_valid){
				$form.card_type = res.data.type?.toUpperCase() || '';
				$form.bank_name = res.data.bank_name?.toUpperCase() || '';
			}
		} catch (error) {
			console.error('Error during API call:', error);
		}
	};

	const handleFormChange = (e) => {
		handleChange(e);
		pageState.update((s) => ({
			...s,
			formFilled: formFilled()
		}));
	};

	// Check for pre-filled card number on component initialization
	$: if ($form.card_number && $form.card_number.length >= 8 && !$form.card_type) {
		// Add a small delay to ensure the component is fully initialized
		setTimeout(() => {
			handleCardTypeBankNameAPICall($form.card_number);
		}, 300);
	}


</script>

<form class:valid={$isValid} class="justify-between" on:submit={handleSubmit}>
	<span class="md:hidden">
		<NavigateBack
			icon="CARD_ICON"
			text="Card"
			click={resetMode}
			hideArrow={availableModes.length == 1}
		/>
	</span>
	<div class="card-number flex flex-col pt-4 md:pt-2 relative">

		<input
			type="text"
			class={`peer block !h-10 w-full pl-3 pr-12 !py-0 text-sm font-semibold text-textPrimary bg-white bg-clip-padding border border-solid border-[#a6a7b0] rounded-md transition ease-in-out m-0
            focus:text-textPrimary focus:bg-white focus:outline-none autofill:bg-white ` +
				($errors.card_number && $touched.card_number ? `border-red-600` : ``)}
			id="card_number"
			name="card_number"
			inputmode="numeric"
			maxlength="19"
			on:input={(e) => {
				$form.card_number_cursor = getCursorPosition(e.srcElement);
				let srcLength = e.target.value.length;
				let formatted = formatCardNumber(e.target.value, creditCardType($form.card_number)[0]);
				let diff = Math.max(0, formatted.length - srcLength);
				e.target.value = formatted;
				handleFormChange(e);
				setCursorPosition(e.srcElement, $form.card_number_cursor + diff);

				const digitsOnly = clearFormatCardNumber($form.card_number);

				if (digitsOnly.length <= 7) {
		          $form.card_type = '';
		          $form.bank_name = '';
	            }

				if (digitsOnly.length > 12) {
					// Card number is long enough for validation
	            }

				// Call the API when exactly 8 digits are entered
				if (digitsOnly.length === 8) {
					handleCardTypeBankNameAPICall($form.card_number);
				}
			}}
			on:paste={() => {
				// Handle paste event - wait a moment for the input value to update
				setTimeout(() => {
					if ($form.card_number && $form.card_number.length >= 8) {
						handleCardTypeBankNameAPICall($form.card_number);
					}
				}, 300); // Increased timeout to ensure input is fully updated
			}}
			value={$form.card_number}
			placeholder={cardConfig.text + ' Number'}
		/>
		<label
			for="card_number"
			class={`text-textSecondary bg-white peer-placeholder-shown:hidden absolute top-2 md:top-0 mx-3 px-0.5 text-xs transform transition-all ` +
				($errors.card_number && $touched.card_number ? `text-red-600` : ``)}
		>
			{cardConfig.text} Number
		</label>
		<small
			class={'error-text pt-2 font-semibold' +
				($errors.card_number && $touched.card_number ? ' text-red-600' : ' text-textSecondary')}
		>
			<!-- {$errors.card_number || 'Only Visa and Master cards are supported'}</small
		> -->
		{$errors.card_number}</small>
		{#if $form.card_type && $form.bank_name}
		    <div>
				<span style="font-size: 12px;">{($form.card_type || '').toUpperCase()}</span>
				<span style="font-size: 12px;">•</span>
				<span style="font-size: 12px;">{($form.bank_name || '').toUpperCase()}</span>
			</div>
		{/if}

		{#if $form.card_number.length >= 3 && creditCardType($form.card_number).length == 1}
			{#if creditCardType($form.card_number)[0]['type'] == CARD_TYPES[0].toLowerCase()}
				<div class="card-type absolute right-4 w-10 top-7 md:top-5">
					<img class="card visa" src={VISA} alt="VISA" />
				</div>
			{:else if creditCardType($form.card_number)[0]['type'] == CARD_TYPES[1].toLowerCase()}
				<div class="card-type absolute right-4 w-6 top-7 md:top-5">
					<img class="card mastercard" src={MASTERCARD} alt="MasterCard" />
				</div>
			{:else if creditCardType($form.card_number)[0]['type'] == CARD_TYPES[2].toLowerCase()}
				<div class="card-type absolute right-4 w-8 top-8 md:top-6">
					<img class="card rupay" src={RUPAY} alt="Rupay" />
				</div>
			{:else if creditCardType($form.card_number)[0]['type'] == CARD_TYPES[3].toLowerCase()}
				<div class="card-type absolute right-4 w-6 top-7 md:top-5">
					<img class="card maestro" src={MAESTRO} alt="Maestro" />
				</div>
			{:else if creditCardType($form.card_number)[0]['type'] == CARD_TYPES[4].toLowerCase()}
				<div class="card-type absolute right-4 w-6 top-7 md:top-5">
					<img class="card paypal" src={PAYPAL} alt="Paypal" />
				</div>
			{/if}
		{/if}
	</div>

	<div class="grid grid-cols-[1.4fr_0.6fr] xl:grid-cols-2 gap-2 md:gap-4 pt-2">
		<div class="card-expires-on flex flex-col mt-2 pt-4 md:pt-2 relative">
			<input
				type="text"
				class={`peer block !h-10 w-full px-3 !py-0 text-sm font-semibold text-textPrimary bg-white bg-clip-padding border border-solid border-[#a6a7b0] rounded-md transition ease-in-out m-0
            focus:text-textPrimary focus:bg-white focus:outline-none autofill:bg-white ` +
					($errors.card_expires_on && $touched.card_expires_on ? `border-red-600` : ``)}
				id="card_expires_on"
				name="card_expires_on"
				maxlength="5"
				inputmode="numeric"
				on:input={(e) => {
					e.target.value = e.target.value.split('/').join('');
					handleFormChange(e);
				}}
				value={formatCardExpiresOn($form.card_expires_on)}
				placeholder="Expires On (MM/YY)"
			/>
			<label
				for="card_number"
				class={`text-textSecondary bg-white peer-placeholder-shown:hidden absolute top-2 md:top-0 mx-3 px-0.5 text-xs transform transition-all ` +
					($errors.card_expires_on && $touched.card_expires_on ? `text-red-600` : ``)}
			>
				Expires On (MM/YY)
			</label>
			{#if $errors.card_expires_on && $touched.card_expires_on}
				<small class="error-text pt-2 text-red-600">{$errors.card_expires_on}</small>
			{/if}
		</div>

		<div class="card-cvv flex flex-col mt-2 pt-4 md:pt-2 relative">
			<input
				type="text"
				class={`peer block !h-10 w-full md:w-4/5 xl:w-2/5 px-3 !py-0 text-sm font-semibold text-textPrimary bg-white bg-clip-padding border border-solid border-[#a6a7b0] rounded-md transition ease-in-out m-0
            focus:text-textPrimary focus:bg-white focus:outline-none autofill:bg-white ` +
					($errors.card_cvv && $touched.card_cvv ? `border-red-600` : ``)}
				id="card_cvv"
				name="card_cvv"
				maxlength="3"
				inputmode="numeric"
				on:input={handleFormChange}
				value={$form.card_cvv}
				placeholder="CVV"
			/>
			<label
				for="card_cvv"
				class={`text-textSecondary bg-white peer-placeholder-shown:hidden absolute top-2 md:top-0 mx-3 px-0.5 text-xs transform transition-all ` +
					($errors.card_cvv && $touched.card_cvv ? `text-red-600` : ``)}
			>
				CVV
			</label>
			{#if $errors.card_cvv && $touched.card_cvv}
				<small class="error-text pt-2 text-red-600">{$errors.card_cvv}</small>
			{/if}
		</div>
	</div>

	<div class="card-holder-name flex flex-col mt-4 pt-4 md:pt-2 relative">
		<input
			type="text"
			class={`peer block !h-10 w-full px-3 !py-0 text-sm font-semibold text-textPrimary bg-white bg-clip-padding border border-solid border-[#a6a7b0] rounded-md transition ease-in-out m-0
            focus:text-textPrimary focus:bg-white focus:outline-none autofill:bg-white ` +
				($errors.card_holder_name && $touched.card_holder_name ? `border-red-600` : ``)}
			id="card_holder_name"
			name="card_holder_name"
			on:input={handleFormChange}
			value={$form.card_holder_name}
			placeholder="Card Holder Name"
		/>
		<label
			for="card_holder_name"
			class={`text-textSecondary bg-white peer-placeholder-shown:hidden absolute top-2 md:top-0 mx-3 px-0.5 text-xs transform transition-all ` +
				($errors.card_holder_name && $touched.card_holder_name ? `text-red-600` : ``)}
		>
			Card Holder Name
		</label>
		{#if $errors.card_holder_name && $touched.card_holder_name}
			<small class="error-text pt-2 text-red-600">{$errors.card_holder_name}</small>
		{/if}
	</div>

	{#if !subscription.authorization_amount || (!!subscription.authorization_amount && parseInt(subscription.authorization_amount, 10) <= 1)}
		<div class="mt-4 text-textSecondary text-sm">
			To complete the authorization, an amount of {subscription.currency} {subscription.authorization_amount} will be deducted from your account.
			<!-- <Tooltip text="In order to create a Subscription successfully, the card has to be saved as per guidelines.">
				<img
					src={INFO_ICON}
					alt="info"
					class="inline ml-1 w-4 h-4"
				/>
			</Tooltip> -->
		</div>
	{/if}

	<div class="authorization flex pt-4">
		<label class="form-check-label cursor-pointer relative">
		  <input
			class="hidden"
			type="checkbox"
			checked={$form.is_authorized}
			id="is_authorized"
			name="is_authorized"
			on:change={handleFormChange}
		  />
		  <span class="tick w-4 h-4 mb-4 inline-block absolute rounded-sm border-2 border-textSecondary">
			<div class="flex justify-center h-full items-center">
			  <svelte:component this={ICON_AUTHORIZE_TICK} />
			</div>
		  </span>
		  <div class="text-xs font-semibold ml-7" style="font-weight: 600;">
			I authorize {merchant.merchant_meta.merchant_name} to debit the amount mentioned above from my
			card as per the payment instructions stated. I agree to tokenise my card details as per regulatory guidelines.
		  </div>
		</label>
	  </div>

	<div class="flex pt-8">
		<!--TODO: Handle all form values which are not touched -->
		<!--  -->
		<button
			type="submit"
			disabled={!$isValid || !$pageState.formFilled}
			class={`grow inline-block h-10 px-6 py-2.5 font-semibold text-sm leading-tight rounded-md focus:outline-none focus:ring-0 transition duration-150 ease-in-out` +
				(!$isValid || !$pageState.formFilled || $isSubmitting
					? ` cursor-not-allowed bg-buttonDisabled text-textSecondary`
					: ` bg-productPrimary text-white`)}
		>
			{#if $isSubmitting}<span class="relative -top-0.5"
					><Loader enabledMode={!$isSubmitting} /></span
				>{:else}Next{/if}
		</button>
	</div>
</form>
<PoweredBy selectedMode={PAYMENT_MODES[2]} />

<style>
	.card-cvv input {
		-webkit-text-security: disc;
		-moz-webkit-text-security: disc;
		-moz-text-security: disc;
	}
</style>
