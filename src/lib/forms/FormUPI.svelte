<script>
	import { onMount } from 'svelte/internal';
	import { writable } from 'svelte/store';
	import { afterUpdate, onDestroy } from 'svelte';
	import { createForm } from 'svelte-forms-lib';
	import * as yup from 'yup';

	// Components
	import Loader from '$components/Loader.svelte';
	import TPV from '$components/TPV.svelte';
	import Authorization from '$components/Authorization.svelte';
	import NavigateBack from '$components/NavigateBack.svelte';
	import PoweredBy from '$components/PoweredBy.svelte';
	import QRCode from '$components/QRCode.svelte';
	import OR from '$components/OR.svelte';
	import AppTray from '$components/AppTray.svelte';
	import CancelRegistration from '$components/CancelRegistration.svelte';

	// Constants
	import {
		AUTH_STATUS,
		PAYMENT_MODES,
		PAYMENT_MODES_CONFIG,
		SBC_PAYMENT_MODE,
		UPI_FLOWS,
		OS,
		UPI_APPS_CONFIG,
		UPI_APPS,
		PROD_TEST_MERCHANT_IDS
	} from '$utils/constants';

	// Icons
	import Overlay from '$lib/Overlay.svelte';
	import ICON_UPI from '$lib/Icons/upi.svelte';
	import ICON_QR from '$lib/Icons/QR.svelte';

	// Helpers
	import {
		detectSdk,
		getAppListFromSDK,
		getIOSAppListFromSDK,
		isSupportedUPI,
		redirectionForAndroid,
		redirectionForIOS,
		removeKeyFromQueryParams
	} from '$utils/helpers';
	import { track } from '$utils/amplitude';
	import { IS_NON_PROD, ENV_APP_BASE_PATH } from '$lib/Env';
	import { UI_PATHS } from '$utils/path';
	import OpenAppModal from '$components/OpenAppModal.svelte';
	import { info, log } from 'loglevel';

	export let token;
	export let merchant;
	export let subscription;
	export let formState;
	export let verifyUPI;
	export let updateVerifyUPI;
	export let paymentOptions = PAYMENT_MODES_CONFIG;
	export let handleUPISubmit;
	export let resetMode;
	export let availableModes;
	export let amplitudeEvent;
	export let getAuthStatus;
	export let mandateAuthData;
	export let isConfirmSubmitting;
	export let os;

	let timer;
	let polling;
	let eMandateConfig = paymentOptions[PAYMENT_MODES[1]];
	let upi_id_selection = false;
	let App = '';
	let qrData;
	let selectedApp = '';
	let intentUrl;

	yup.addMethod(yup.string, 'validUPI', function (message) {
		return this.test('is_valid_upi_ud', message, function (val) {
			return isSupportedUPI(val, eMandateConfig.handles);
		});
	});

	const {
		// observables
		state,
		form,
		errors,
		touched,
		isValid,
		isSubmitting,
		isValidating,
		// handlers
		handleChange,
		handleSubmit,
		handleReset
	} = createForm({
		initialValues: {
			...formState,
			upi_id: formState.upi_id || '',
			upi_id_selection: formState.upi_id_selection || '',
			upi_postfix: formState.upi_postfix || '',
			verification_status: formState.verification_status || '',
			upi_auth_flow: formState.upi_auth_flow || ''
		},
		validationSchema: yup.object().shape({
			upi_auth_flow: yup.string(),
			// TODO: Add validUPI to global.d.ts
			upi_id: yup.string().when('upi_auth_flow', {
				is: UPI_FLOWS.COLLECT,
				then: yup.string().validUPI('UPI ID is invalid or not supported'),
				otherwise: yup.string().notRequired()
			})
		}),
		onSubmit: async (f) => {
			if ($pageState.selectedFlow === UPI_FLOWS.QR) {
				$form.upi_auth_flow = UPI_FLOWS.QR;
				handleUPISubmit(f);
			} else if ($pageState.selectedFlow === UPI_FLOWS.INTENT) {
				$form.upi_auth_flow = UPI_FLOWS.INTENT;
				handleUPISubmit(f);
			} else {
				$form.upi_auth_flow = UPI_FLOWS.COLLECT;
			}
			if (
				$form.upi_auth_flow === UPI_FLOWS.COLLECT &&
				isSupportedUPI(f.upi_id, eMandateConfig.handles)
			) {
				$form.upi_id_selection = upi_id_selection === true ? 'dropdown' : 'typed';
				$form.upi_postfix = $form.upi_id.split('@')[1];
				if (!!eMandateConfig.validate_UPI) {
					const { err, res } = await verifyUPI({ upi_id: $form.upi_id }, token);
					$form.verification_status = !err
						? 'SUCCESS'
						: `'FAILURE': ${res.data.validation_message}`;
					if (!!res && !res.data.is_valid && !IS_NON_PROD) {
						$errors.upi_id = 'Invalid UPI ID';
						updateVerifyUPI({ data: { upi_id: f.upi_id, upi_name: '' } });
					} else {
						updateVerifyUPI({ ...res });
						handleUPISubmit(f);
						track(
							'CHECKOUT_FORM_SUBMIT',
							{
								merchantType: merchant.merchant_meta.merchant_type,
								linkCreationSource: subscription.source,
								authID: '',
								subscriptionMode: 'UPI',
								upi_postfix: $form.upi_postfix,
								upi_id_selection: $form.upi_id_selection,
								paymentMode: SBC_PAYMENT_MODE.SBC_UPI,
								submissionStatus: $form.verification_status
							},
							amplitudeEvent,
							token
						);
					}
					track(
						'CHECKOUT_VERIFICATION',
						{
							merchantType: merchant.merchant_meta.merchant_type,
							linkCreationSource: subscription.source,
							authID: '',
							subscriptionMode: 'UPI',
							upi_postfix: $form.upi_postfix,
							upi_id_selection: $form.upi_id_selection,
							paymentMode: SBC_PAYMENT_MODE.SBC_UPI,
							verificationStatus: $form.verification_status
						},
						amplitudeEvent,
						token
					);
				} else {
					updateVerifyUPI({ data: { upi_id: f.upi_id, upi_name: '' } });
					handleUPISubmit(f);
					track(
						'CHECKOUT_FORM_SUBMIT',
						{
							merchantType: merchant.merchant_meta.merchant_type,
							linkCreationSource: subscription.source,
							authID: '',
							subscriptionMode: 'UPI',
							upi_postfix: $form.upi_postfix,
							upi_id_selection: $form.upi_id_selection,
							paymentMode: SBC_PAYMENT_MODE.SBC_UPI,
							submissionStatus: 'SUCCESS'
						},
						amplitudeEvent,
						token
					);
				}
			}
		}
	});

	const formFilled = () => {
		return $form.upi_id != '';
	};

	const pageState = writable({
		formFilled: formFilled(),
		filteredHandles: [],
		isPopupVisible: false,
		selectedFlow: null,
		authStatus: AUTH_STATUS.CREATED,
		isCancelModal: false,
		isOpenAppModal: false,
		seconds: 5 * 60,
		upiApps: [],
		qrApps: [],
		isIntentSupported: false,
		isQrSupported: false,
		isCollectSupported: false,
		onSdk: false
	});

	let isCancelModal = $pageState.isCancelModal;
	let isOpenAppModal = $pageState.isOpenAppModal;
	let timerSeconds = $pageState.seconds;

	const handleModalCancel = () => {
		pageState.update((s) => ({
			...s,
			isCancelModal: false
		}));
	};

	const handleFormChange = (e) => {
		// Debounce trigger upi id validation
		handleChange(e);
		pageState.update((s) => ({
			...s,
			formFilled: formFilled(),
			isPopupVisible: isPopupVisible(e),
			filteredHandles: filterHandles()
		}));
	};

	const filterHandles = () => {
		let upiID = $form.upi_id.split('@');
		if (upiID.length == 2 && upiID[1] != '') {
			let filters = eMandateConfig.available_handles.filter((v) => v.handle.startsWith(upiID[1]));
			return filters;
		}
		return eMandateConfig.available_handles;
	};

	const isPopupVisible = (e) => {
		let upiID = $form.upi_id.split('@');
		if (
			upiID.length == 2 &&
			upiID[1] != '' &&
			!isSupportedUPI($form.upi_id, eMandateConfig.handles)
		) {
			return true;
		}
		return false;
	};

	const handleSelection = (e) => {
		$form.upi_id = `${$form.upi_id.split('@')[0]}@${e.target.getAttribute('data-value')}`;
		$errors.upi_id = '';
		pageState.update((s) => ({
			...s,
			formFilled: formFilled(),
			isPopupVisible: false
		}));
		upi_id_selection = true;
	};

	const handleUPIFlow = (upiFlow) => {
		if (upiFlow === null) {
			handleReset();
		}
		pageState.update((s) => ({
			...s,
			selectedFlow: upiFlow
		}));
	};

	const updateAuthStatus = ({ err, res }) => {
		if (!err)
			pageState.update((s) => ({
				...s,
				authStatus: res.data.auth_status
			}));
	};

	const handleQRBack = () => {
		pageState.update((s) => ({
			...s,
			isCancelModal: true
		}));
	};

	const handleBack = () => {
		handleUPIFlow(null);
	};

	const handleQRPolling = () => {
		timer = setInterval(() => {
			if (timerSeconds) {
				pageState.update((s) => ({
					...s,
					seconds: timerSeconds - 1
				}));
			} else {
				clearInterval(timer);
				clearInterval(polling);
				location.href = `${ENV_APP_BASE_PATH}${UI_PATHS.STATUS}/${token}`;
			}
		}, 1000);
		polling = setInterval(async () => {
			if (
				$pageState.authStatus != AUTH_STATUS.ACTIVE &&
				$pageState.authStatus != AUTH_STATUS.FAILED
			) {
				let { err, res } = await getAuthStatus(token, mandateAuthData?.formData?.authID);
				updateAuthStatus({ err, res });
			} else {
				clearInterval(polling);
				location.href = `${ENV_APP_BASE_PATH}${UI_PATHS.STATUS}/${token}`;
			}
		}, 5000);
	};

	const handleRedirection = async () => {
		if (App) {
			if (IS_NON_PROD) return;
			const a = document.createElement('a');
			const link = document.createTextNode('');
			a.appendChild(link);
			a.title = '';
			let intentUrl = '';

			if (os === OS.ANDROID) {
				intentUrl = redirectionForAndroid(App, qrData);
				a.target = '_blank';
			} else if (os === OS.IOS) {
				intentUrl = redirectionForIOS(App, qrData);
				a.target = '_top';
			}
			a.href = intentUrl;

			// TODO: handle ios sdk flow
			if ($pageState.onSdk) {
				const isAndroidSdk = !!window.Android;
				const isIosSdk = !!(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.nativeProcess);
				
				if (isAndroidSdk) {
					switch (App) {
						case UPI_APPS[0]:
							const upiPayQuery = qrData.split('?')[1];
							qrData = `upi://mandate?${removeKeyFromQueryParams(upiPayQuery, 'sign')}`;
							break;
						case UPI_APPS[1]:
							qrData = intentUrl;
							break;
						default:
							break;
					}
					window.Android.openApp(UPI_APPS_CONFIG[App].id, qrData);
				} else if (isIosSdk) {
					window.webkit.messageHandlers.nativeProcess.postMessage(intentUrl);
				}
			} else {
				a.click();
			}
		}
	};


	const handleAppClick = (app) => {
		App = app;
		qrData = mandateAuthData?.formData?.qrData;
	};

	const unsubscribe = pageState.subscribe((v) => {
		isCancelModal = v.isCancelModal;
		isOpenAppModal = v.isOpenAppModal;
		timerSeconds = v.seconds;
	});

	afterUpdate(() => {
		qrData = mandateAuthData?.formData?.qrData;
		if (qrData !== undefined) handleRedirection();
	});

	onMount(async () => {
		const upiIntent = paymentOptions[PAYMENT_MODES[1]].intentSupport.find(
			(osApp) => osApp.operatingSystem.toUpperCase() === os.toUpperCase()
		);

		let upiApps = upiIntent ? upiIntent.applications.filter((app) => app !== 'DEFAULT') : [];

		const isAndroidSdk = !!window.Android;
		const isIosSdk = !!(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.nativeProcess);
		const onSdk = isAndroidSdk || isIosSdk;

		if (detectSdk('android')) {
			upiApps = getAppListFromSDK(true, upiApps);
		} else if (detectSdk('ios')) {
			upiApps = await getIOSAppListFromSDK(true, upiApps);
		}

		const isIntentSupported =
			paymentOptions[PAYMENT_MODES[1]].isIntentSupported && upiApps.length > 0;

		const isQrSupported =
			paymentOptions[PAYMENT_MODES[1]].isQrSupported &&
			paymentOptions[PAYMENT_MODES[1]].qrSupport.length > 0;

		pageState.update((s) => ({
			...s,
			onSdk: onSdk,
			upiApps: upiApps,
			qrApps: paymentOptions[PAYMENT_MODES[1]].qrSupport,
			isIntentSupported: isIntentSupported,
			isQrSupported: isQrSupported,
			isCollectSupported: paymentOptions[PAYMENT_MODES[1]].isCollectSupported
		}));
	});

	onDestroy(unsubscribe);
</script>

<form class="justify-between" class:valid={$isValid} on:submit={handleSubmit}>
	<span class="md:hidden">
		<NavigateBack
			icon="UPI_ICON"
			text="UPI"
			click={resetMode}
			hideArrow={availableModes.length == 1}
		/>
	</span>
	{#if !$pageState.selectedFlow}
		{#if !subscription.authorization_amount || (!!subscription.authorization_amount && parseInt(subscription.authorization_amount, 10) <= 1)}
			<Authorization refundAuthAmount={subscription.refund_auth_amount} />
		{/if}
		{#if subscription.tpv_enabled}
			<TPV tpv_data={subscription.payment_instrument_details} />
		{/if}
		<!-- Choose flow -->
		<div class="flex flex-col cursor-pointer ">
			{#if $pageState.isQrSupported}
				<div class="hidden sm:block">
					<div
						class="p-6 shadow-[0_2px_8px_rgba(125,68,240,0.12)] rounded-lg mb-6 hover:bg-productPrimaryHover"
						on:click={() => {
							handleUPIFlow(UPI_FLOWS.QR);
							handleSubmit($form);
							handleQRPolling();
						}}
					>
						<span class={`flex items-center gap-4 pt-0.5 pr-2 relative bottom-0.5`}>
							<svelte:component this={ICON_QR} />
							{#if $isSubmitting}
								<span class="relative -top-0.5">
									<Loader enabledMode={!$isSubmitting} />
								</span>
							{:else}Scan QR{/if}
						</span>
					</div>
				</div>
			{/if}

			{#if $pageState.isIntentSupported}
				<div class="md:hidden flex flex-row flex-wrap justify-start gap-2">
					<!-- UPI Apps -->
					{#each $pageState.upiApps as app}
						{#if app !== 'DEFAULT'}
							<div
								class="p-2 text-center shadow-[0_2px_8px_rgba(125,68,240,0.12)] rounded-lg mb-6 hover:bg-productPrimaryHover w-18 h-16"
								on:click={() => {
									if (os === OS.ANDROID) {
										handleUPIFlow(UPI_FLOWS.INTENT);
										handleSubmit($form);
										handleAppClick(app);
									} else if (os === OS.IOS) {
										pageState.update((s) => ({
											...s,
											isOpenAppModal: true
										}));
										selectedApp = app;
									}
								}}
							>
								<AppTray {app} />
							</div>
						{/if}
					{/each}
				</div>
			{/if}

			{#if $pageState.isIntentSupported && $pageState.isCollectSupported}
				<div class="md:hidden">
					<OR />
				</div>
			{/if}

			{#if $pageState.isCollectSupported}
				<div
					class="flex justify-between items-baseline p-6 shadow-[0_2px_8px_rgba(125,68,240,0.12)] rounded-lg hover:bg-productPrimaryHover"
					on:click={() => handleUPIFlow(UPI_FLOWS.COLLECT)}
				>
					<span class="flex items-center gap-4 pt-0.5 pr-2 relative bottom-0.5">
						<svelte:component this={ICON_UPI} />
						<span>Enter UPI ID</span>
					</span>
				</div>
			{/if}
		</div>
	{:else if $pageState.selectedFlow === UPI_FLOWS.QR}
		<NavigateBack click={handleQRBack} />
		<div class="qr-upi flex flex-col justify-center items-center pt-8">
			{#if subscription.tpv_enabled}
				<TPV tpv_data={subscription.payment_instrument_details} classes="text-center" />
			{/if}

			<QRCode text={removeKeyFromQueryParams(mandateAuthData?.formData?.qrData, 'sign')} qrApps={$pageState.qrApps} />
		</div>
	{:else if $pageState.selectedFlow === UPI_FLOWS.COLLECT}
		<div class="flex flex-col">
			<NavigateBack click={handleBack} />
			{#if !subscription.authorization_amount || (!!subscription.authorization_amount && parseInt(subscription.authorization_amount, 10) <= 1)}
				<Authorization />
			{/if}
			{#if subscription.tpv_enabled}
				<TPV tpv_data={subscription.payment_instrument_details} />
			{/if}
			<div class="px-6 pt-2 pb-6 md:py-6 shadow-[0_2px_8px_rgba(125,68,240,0.12)] rounded-lg mb-6">
				<span
					class="hidden md:!flex items-center gap-4 pt-0.5 pb-2 pr-2 relative bottom-0.5 text-xs text-textSecondary font-semibold"
				>
					<svelte:component this={ICON_UPI} />
					<span>Enter UPI ID</span>
				</span>
				<div class="upi-id flex flex-col pt-4 md:pt-2 relative">
					<input
						type="text"
						class={`peer block !h-10 w-full px-3 !py-0 text-sm font-semibold text-textPrimary bg-white bg-clip-padding border border-solid border-[#a6a7b0] rounded-md transition ease-in-out m-0
				focus:text-textPrimary focus:bg-white focus:outline-none autofill:bg-white ` +
							($errors.upi_id && $touched.upi_id ? `border-red-600` : ``)}
						id="upi_id"
						name="upi_id"
						on:input={handleFormChange}
						value={$form.upi_id}
						placeholder="UPI ID"
					/>
					{#if $pageState.isPopupVisible && !!$pageState.filteredHandles.length}
						<div
							class="popup absolute top-14 px-4 py-2 bg-white shadow-[0_-3px_10px_rgba(43,45,66,0.14)]"
							style="left:{`min(10ch,${$form.upi_id.split('@')[0].length}ch)`}"
						>
							<ul>
								{#each $pageState.filteredHandles as handle}
									<li
										class="handle p-2 cursor-pointer"
										data-value={handle.handle}
										on:click={handleSelection}
									>
										@{handle.handle}
									</li>
								{/each}
							</ul>
						</div>
					{/if}
					{#if $errors.upi_id && $touched.upi_id}
						<small class="error-text pt-2 pl-3 md:pl-0 text-red-600">{$errors.upi_id}</small>
					{/if}
					<small class="pt-2">
						UPI ID should in the format <span class="font-semibold">username@upihandle</span></small
					>
				</div>
				<div class="flex pt-8">
					<!--TODO: Handle all form values which are not touched -->
					<button
						type="submit"
						disabled={!$isValid || !$pageState.formFilled || $isSubmitting || isConfirmSubmitting}
						class={`grow inline-block h-10 px-6 py-2.5 font-semibold text-sm leading-tight rounded-md focus:outline-none focus:ring-0 transition duration-150 ease-in-out` +
							(!$isValid || !$pageState.formFilled || $isSubmitting || isConfirmSubmitting
								? ` cursor-not-allowed bg-buttonDisabled text-textSecondary`
								: ` bg-productPrimary text-white`)}
					>
						{#if $isSubmitting || isConfirmSubmitting}
							<span class="relative -top-0.5">
								<Loader enabledMode={!$isSubmitting && !isConfirmSubmitting} />
							</span>
						{:else}Verify & Proceed{/if}
					</button>
				</div>
			</div>
		</div>
	{/if}
</form>
<PoweredBy selectedMode={PAYMENT_MODES[1]} />
<Overlay isOverlay={isOpenAppModal}>
	<OpenAppModal
		app={selectedApp}
		url={intentUrl}
		handleReject={() => {
			pageState.update((s) => ({
				...s,
				isOpenAppModal: false
			}));
		}}
		handleConfirm={() => {
			handleUPIFlow(UPI_FLOWS.INTENT);
			handleSubmit($form);
			handleAppClick(selectedApp);
			pageState.update((s) => ({
				...s,
				isOpenAppModal: false
			}));
		}}
	/>
</Overlay>
<Overlay isOverlay={isCancelModal}>
	<CancelRegistration
		selectedMode={PAYMENT_MODES[1]}
		handleReject={handleModalCancel}
		handleConfirm={() => {
			clearInterval(timer);
			clearInterval(polling);
			$pageState.seconds = 5 * 60;
			handleBack();
			pageState.update((s) => ({
				...s,
				isCancelModal: false
			}));
		}}
	/>
</Overlay>