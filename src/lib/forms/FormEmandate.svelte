<script>
	import Select from 'svelte-select';
	import { clickOutside } from '$utils/clickOutisde';
	import { writable } from 'svelte/store';

	// Libraries
	import { addToast } from '$lib/Toast';
	import { createForm } from 'svelte-forms-lib';
	import * as yup from 'yup';

	// Components
	import Loader from '$components/Loader.svelte';
	import NavigateBack from '$components/NavigateBack.svelte';
	import PoweredBy from '$components/PoweredBy.svelte';

	// Icons
	import ICON_AUTHORIZE_TICK from '$lib/Icons/authorize_tick.svelte';
	import ICON_TICK from '$lib/Icons/tick.svelte';
	import ICON_TICK_WHITE from '$lib/Icons/tick_white.svelte';
	import ICON_INFORMATION from '$lib/Icons/i.svelte';
	import ICON_UNION from '$lib/Icons/union.svelte';
	import TPV_UNION from '$lib/Icons/tpv_bank.svelte';

	// Constants
	import {
		PAYMENT_MODES,
		ACCOUNT_TYPES,
		ACCOUNT_TYPES_LABELS,
		ACCOUNT_AUTHENTICATION_TYPE,
		ACCOUNT_AUTHENTICATION_TYPES_LABELS,
		PAYMENT_MODES_CONFIG,
		SBC_PAYMENT_MODE
	} from '$utils/constants';
	import { track } from '$utils/amplitude';

	export let formState;
	export let merchant;
	export let paymentOptions = PAYMENT_MODES_CONFIG;
	export let handleEmandateSubmit;
	export let step;
	export let updateCurrentStep;
	export let resetMode;
	export let availableModes;
	export let verifyAuthAttempt;
	export let token;
	export let amplitudeEvent;
	export let subscription;

	let eMandateConfig = paymentOptions[PAYMENT_MODES[0]];
	let tpv_auth_mode = paymentOptions[PAYMENT_MODES[0]].all_banks.find(
		(ele) => ele.bank_id == subscription?.payment_instrument_details?.bank_id
	);
	const optionIdentifier = 'bank_id';
	const labelIdentifier = 'bank_display_name';
	//TODO: Figure out a way to move this to separate file
	const DOWN_ARROW = `<svg width="14" height="8" viewBox="0 0 14 8" fill="none" xmlns="http://www.w3.org/2000/svg">
		<path
			d="M13.7071 1.70711C14.0976 1.31658 14.0976 0.683418 13.7071 0.292893C13.3371 -0.077077 12.7494 -0.0965491 12.3565 0.234476L12.2929 0.292893L7 5.5855L1.70711 0.292892C1.33714 -0.077078 0.749395 -0.0965502 0.356501 0.234475L0.292894 0.292892C-0.0770763 0.662863 -0.0965484 1.2506 0.234478 1.6435L0.292894 1.70711L6.29289 7.70711C6.66286 8.07708 7.25061 8.09655 7.6435 7.76552L7.70711 7.70711L13.7071 1.70711Z"
			fill="var(--product-primary,#6930CA)"
		/>
	</svg>`;
	let filterText = '';

	const {
		// observables
		state,
		form,
		errors,
		touched,
		isValid,
		isSubmitting,
		isValidating,
		// handlers
		handleChange,
		handleSubmit
	} = createForm({
		initialValues: {
			...formState,
			is_authorized: formState.is_authorized || eMandateConfig.is_authorized,
			tpv_enabled: subscription.tpv_enabled,
			payment_instrument_details: subscription.payment_instrument_details,
			...(subscription.tpv_enabled
				? {
						auth_mode: formState.auth_mode || tpv_auth_mode.auth[0],
						account_type: subscription.payment_instrument_details.account_type,
						bank_id: subscription.payment_instrument_details.bank_id,
						account_number: subscription.payment_instrument_details.account_number,
						account_holder_name: subscription.payment_instrument_details.account_holder_name
				  }
				: {
						auth_mode: formState.auth_mode || eMandateConfig.all_banks[0].auth[0],
						account_type: formState.account_type || eMandateConfig.account_type[0],
						bank_id: formState.bank_id || ''
				  })
		},
		validationSchema: yup.object().shape({
			bank_id: yup.string(),
			account_type: yup.string(),
			account_number: yup
				.string()
				.max(35, 'Must be less than 36 characters')
				.required('Account Number cannot be empty')
				.matches(/^[0-9a-zA-Z]+$/, `Invalid Acccount Number`),
			account_holder_name: yup
				.string()
				.max(40, 'Must be less than or 40 characters')
				.required('Account Holder Name cannot be empty')
				.matches(/^[0-9a-zA-Z]+[0-9a-zA-Z ]*$/, 'Invalid Acccount Name'),
			auth_mode: yup.string(),
			is_authorized: yup.boolean().oneOf([true], 'This field must be checked')
		}),
		onSubmit: async (f) => {
			const { err, res } = await verifyAuthAttempt(
				{
					account_number: $form.account_number,
					bank_id: $form.bank_id
				},
				PAYMENT_MODES[0],
				token
			);
			if (!!err || !res.data.is_valid) {
				// Hack to disable Next button if Verify - Emandate call sends invalid
				pageState.update((s) => ({
					...s,
					formFilled: false
				}));

				addToast(res.data.validation_message, 'warn', 3000);
			} else {
				handleEmandateSubmit(f);
			}
			track(
				'CHECKOUT_FORM_SUBMIT',
				{
					linkCreationSource: subscription.source,
					merchantType: merchant.merchant_meta.merchant_type,
					authID: '',
					accountType: $form.account_type,
					accountTypeField: eMandateConfig.account_type.length > 1 ? 'shown' : 'hidden',
					bank: $form.bank_id || '',
					authorizationMode: $form.auth_mode,
					submissionStatus: !err ? 'SUCCESS' : `'FAILURE': ${res.data.validation_message}`,
					paymentMode: SBC_PAYMENT_MODE.SBC_NPCI,
					subscriptionMode: 'Bank Account',
					netbankingAuthMode: ACCOUNT_AUTHENTICATION_TYPE[0] ? 'shown' : 'hidden',
					debitCardAuthModeField: ACCOUNT_AUTHENTICATION_TYPE[1] ? 'shown' : 'hidden',
					aadhaarAuthModeField: ACCOUNT_AUTHENTICATION_TYPE[2] ? 'shown' : 'hidden'
				},
				amplitudeEvent,
				token
			);
		}
	});

	// Added as bug in svelte-form-lib Initial value of $isValid is true
	const formFilled = () => {
		return !(
			$form.is_authorized === false ||
			$form.account_holder_name === '' ||
			$form.account_number === ''
		);
	};

	const pageState = writable({
		selectedBank:
			paymentOptions[PAYMENT_MODES[0]].all_banks.find((ele) => ele.bank_id == $form.bank_id) ||
			eMandateConfig.all_banks[0],
		formFilled: formFilled()
	});

	const handleNext = () => {
		track(
			'BANK_ACCOUNT_BANK_SELECTION',
			{
				linkCreationSource: subscription.source,
				merchantType: merchant.merchant_meta.merchant_type,
				authID: '',
				accountType: $form.account_type,
				accountTypeField: eMandateConfig.account_type.length > 1 ? 'shown' : 'hidden',
				bank: $form.bank_id || '',
				paymentMode: SBC_PAYMENT_MODE.SBC_NPCI,
				subscriptionMode: 'Bank Account'
			},
			amplitudeEvent,
			token
		);
		let selectedBank = paymentOptions[PAYMENT_MODES[0]].all_banks.find(
			(ele) => ele.bank_id == $form.bank_id
		);
		pageState.update((s) => ({
			...s,
			selectedBank: selectedBank
		}));
		$form.auth_mode = selectedBank.auth[0];
		updateCurrentStep(step + 1);
	};
	const handleBack = () => {
		track(
			'click_bank_account_form_back',
			{
				linkCreationSource: subscription.source,
				merchantType: merchant.merchant_meta.merchant_type,
				authID: '',
				accountType: $form.account_type,
				accountTypeField: eMandateConfig.account_type.length > 1 ? 'shown' : 'hidden',
				bank: $form.bank_id || '',
				paymentMode: SBC_PAYMENT_MODE.SBC_NPCI,
				authorizationMode: $form.auth_mode,
				subscriptionMode: 'Bank Account',
				netbankingAuthMode: ACCOUNT_AUTHENTICATION_TYPE[0] ? 'shown' : 'hidden',
				debitCardAuthModeField: ACCOUNT_AUTHENTICATION_TYPE[1] ? 'shown' : 'hidden',
				aadhaarAuthModeField: ACCOUNT_AUTHENTICATION_TYPE[2] ? 'shown' : 'hidden'
			},
			amplitudeEvent,
			token
		);
		updateCurrentStep(step - 1);
	};

	const resetFormPartial = () => {
		$form.account_number = '';
		$form.account_holder_name = '';
		$form.is_authorized = false;
		$form.auth_mode = eMandateConfig.all_banks[0].auth[0];
	};

	const handleAccountTypeChange = (e) => {
		resetFormPartial();
		handleFormChange(e);
	};

	const handleBankChange = (e) => {
		resetFormPartial();
		handleFormChange(e);
	};

	const handleFormChange = (e) => {
		handleChange(e);
		pageState.update((s) => ({
			...s,
			formFilled: formFilled()
		}));
	};
</script>

<form class="justify-between" class:valid={$isValid} on:submit={handleSubmit}>
	{#if !subscription.tpv_enabled}
		<div class={step == 1 ? 'block' : 'hidden'}>
			<span class="md:hidden"
				><NavigateBack
					icon="BANK_ACCOUNT"
					text="Bank Account"
					click={resetMode}
					hideArrow={availableModes.length == 1}
				/></span
			>
			<div class={eMandateConfig.account_type.length > 1 ? 'visible mb-4' : 'hidden d-none'}>
				<div class="pt-2 md:pt-0 text-textSecondary text-sm font-medium">Account Type</div>
				<div class="account-type flex py-2">
					{#each eMandateConfig.account_type as acc}
						<div class="form-check form-check-inline">
							<label class="flex items-center cursor-pointer">
								<input
									bind:group={$form.account_type}
									class="hidden"
									type="radio"
									name="account_type"
									id="account_type"
									value={acc}
									on:click={handleAccountTypeChange}
								/>
								<span
									class="inp-radio w-4 h-4 inline-block mr-2 rounded-full border-2 border-textSecondary"
								/>
								<span class="mr-2">{ACCOUNT_TYPES_LABELS[acc]}</span>
							</label>
						</div>
					{/each}
				</div>
			</div>

			<div class="text-textSecondary text-sm font-medium">Select Bank</div>
			<div
				class="freq-banks grid grid-rows-2 grid-cols-2 sm:grid-rows-1 sm:grid-cols-4 md:grid-cols-2 2xl:grid-rows-1 2xl:grid-cols-4 py-2 gap-2"
			>
				{#each eMandateConfig.frequent_banks as bank}
					<div
						class={`form-check border border-solid border-[#a6a7b0] rounded ` +
							($form.bank_id == bank.bank_id ? `border-productPrimary` : ``)}
					>
						<label class="form-check-label px-2 py-2 flex cursor-pointer">
							<input
								bind:group={$form.bank_id}
								class="hidden"
								type="radio"
								name="bank_id"
								id="bank_id"
								value={bank.bank_id}
								on:click={handleBankChange}
							/>
							<span
								class="tick w-4 h-4 inline-block mr-1.5 mt-0.5 rounded-full border border-textSecondary"
							>
								<div class="flex justify-center h-full items-center">
									{#if $form.bank_id == bank.bank_id}
										<svelte:component this={ICON_TICK_WHITE} />
									{:else}
										<svelte:component this={ICON_TICK} />
									{/if}
								</div>
							</span>
							<img class="h-5" src={bank.bank_logo} alt={bank.bank_display_name} />
						</label>
					</div>
				{/each}
			</div>
			<div
				use:clickOutside
				on:click_outside={(event) => {
					filterText = '';
				}}
				class="all-banks grid grid-cols-2 py-2"
			>
				<Select
					id="bank_id"
					containerClasses={`select-bank-container w-full grow-0 col-span-2 ${
						!!$form.bank_id ? ' hide-input-cursor' : ''
					}`}
					placeholder="Search for a different bank"
					{optionIdentifier}
					{labelIdentifier}
					showIndicator={true}
					items={eMandateConfig.all_banks}
					noOptionsMessage={`No Results for "${filterText}"`}
					indicatorSvg={DOWN_ARROW}
					bind:filterText
					value={eMandateConfig.all_banks.find((b) => b.bank_id == $form.bank_id)}
					on:select={(e) => {
						resetFormPartial();
						$form.bank_id = e.detail.bank_id;
					}}
					on:clear={() => ($form.bank_id = '')}
				/>
			</div>
			<div class="next flex my-6">
				<button
					type="button"
					disabled={$form.bank_id == ''}
					on:click={handleNext}
					class={`grow inline-block h-10 py-2.5 font-semibold text-sm leading-tight rounded-md focus:outline-none focus:ring-0 transition duration-150 ease-in-out` +
						($form.bank_id == ''
							? ` cursor-not-allowed bg-buttonDisabled text-textSecondary`
							: ` bg-productPrimary text-white`)}
				>
					Next
				</button>
			</div>
		</div>
		<div class={step == 2 ? 'block' : 'hidden'}>
			<NavigateBack click={handleBack} />
			<div class="account-number flex flex-col pt-4 relative">
				<input
					type="text"
					class={`peer block !h-10 w-full px-3 !py-0 text-sm font-semibold text-textPrimary bg-white bg-clip-padding border border-solid border-[#a6a7b0] rounded-md transition ease-in-out m-0
				focus:text-textPrimary focus:bg-white focus:outline-none autofill:bg-white ` +
						($errors.account_number && $touched.account_number ? `border-red-600` : ``)}
					id="account_number"
					name="account_number"
					on:input={handleFormChange}
					value={$form.account_number}
					placeholder={$form.account_type === ACCOUNT_TYPES[1]
						? 'Account Number'
						: 'Savings Account Number'}
				/>
				<label
					for="account_number"
					class={`text-textSecondary bg-white peer-placeholder-shown:hidden absolute top-2 mx-3 px-0.5 text-xs transform transition-all ` +
						($errors.account_number && $touched.account_number ? `text-red-600` : ``)}
				>
					{$form.account_type === ACCOUNT_TYPES[1] ? 'Account Number' : 'Savings Account Number'}
				</label>
				{#if $errors.account_number && $touched.account_number}
					<small class="error-text text-red-600">{$errors.account_number}</small>
				{/if}
			</div>
			<div class="account-holder-name flex flex-col pt-6 relative">
				<input
					type="text"
					class={`peer block !h-10 w-full px-3 !py-0 text-sm font-semibold text-textPrimary bg-white bg-clip-padding border border-solid border-[#a6a7b0] rounded-md transition ease-in-out m-0
					focus:text-textPrimary focus:bg-white focus:outline-none autofill:bg-white ` +
						($errors.account_holder_name && $touched.account_holder_name ? `border-red-600` : ``)}
					id="account_holder_name"
					name="account_holder_name"
					value={$form.account_holder_name}
					on:input={handleFormChange}
					placeholder={$form.account_type === ACCOUNT_TYPES[1]
						? 'Account Name'
						: 'Account Holder Name'}
				/>
				<label
					for="account_holder_name"
					class={`text-textSecondary bg-white peer-placeholder-shown:hidden absolute top-4 mx-3 px-0.5 text-xs transform transition-all ` +
						($errors.account_holder_name && $touched.account_holder_name ? `text-red-600` : ``)}
				>
					{$form.account_type === ACCOUNT_TYPES[1] ? 'Account Name' : 'Account Holder Name'}
				</label>
				{#if $errors.account_holder_name && $touched.account_holder_name}
					<small class="error-text text-red-600">{$errors.account_holder_name}</small>
				{/if}
			</div>
			{#if $form.bank_id === 'PYTM'}
				<small class="paytm-text font-light text-zinc-500">
					* Add 91 before your registered phone number
				</small>
			{/if}
			<!-- Multiple supported Authentication types -->
			{#if $pageState.selectedBank.auth?.length > 1}
				<div class="account-authentication font-medium text-sm flex flex-wrap gap-4 pt-6">
					<div>Authenticate Account Using:</div>

					<div class="flex flex-col font-semibold">
						{#each $pageState.selectedBank.auth as authType}
							<div class="form-check form-check-inline pb-2">
								<label class="flex font-medium items-center cursor-pointer">
									<input
										bind:group={$form.auth_mode}
										class="hidden"
										type="radio"
										name="auth_mode"
										id="auth_mode"
										value={authType}
										on:click={handleFormChange}
									/>
									<span
										class="inp-radio w-4 h-4 inline-block mr-2 rounded-full border-2 border-textSecondary"
									/>
									{ACCOUNT_AUTHENTICATION_TYPES_LABELS[authType]}
								</label>
							</div>
						{/each}
					</div>
				</div>
			{:else}
				<div class="account-authentication text-sm font-medium flex pt-6" style="font-weight: 600;">
					<span class="flex pr-2">
						<svelte:component this={ICON_INFORMATION} />
					</span>
					<span>
						Your account will be authenticated via<span class="pl-1.5 font-semibold text-[#D68309]"
							>{ACCOUNT_AUTHENTICATION_TYPES_LABELS[$pageState.selectedBank.auth[0]]}</span
						>
					</span>
				</div>
			{/if}
			<div class="authorization flex pt-4">
				<label class="form-check-label cursor-pointer relative">
					<input
						class="hidden"
						type="checkbox"
						checked={$form.is_authorized}
						id="is_authorized"
						name="is_authorized"
						on:change={handleFormChange}
					/>
					<span
						class="tick w-4 h-4 mb-4 inline-block absolute rounded-sm border-2 border-textSecondary"
					>
						<div class="flex justify-center h-full items-center">
							<svelte:component this={ICON_AUTHORIZE_TICK} />
						</div>
					</span>
					<div class="text-xs font-semibold ml-7" style="font-weight: 600;">
						I authorize {merchant.merchant_meta.merchant_name} to debit the amount mentioned above from
						my account as per the payment instructions stated.
					</div>
				</label>
			</div>
			<div class="flex pt-8">
				<!--TODO: Handle all form values which are not touched -->
				<button
					type="submit"
					disabled={!$isValid || !$pageState.formFilled}
					class={`grow inline-block h-10 px-6 py-2.5 font-semibold text-sm leading-tight rounded-md focus:outline-none focus:ring-0 transition duration-150 ease-in-out` +
						(!$isValid || !$pageState.formFilled || $isSubmitting
							? ` cursor-not-allowed bg-buttonDisabled text-textSecondary`
							: ` bg-productPrimary text-white`)}
				>
					{#if $isSubmitting}
						<span class="relative -top-0.5"><Loader enabledMode={!$isSubmitting} /></span>
					{:else}Next{/if}
				</button>
			</div>
		</div>
	{:else}
		<div class={step == 1 ? 'block' : 'hidden'}>
			<span class="md:hidden">
				<NavigateBack
					icon="BANK_ACCOUNT"
					text="Bank Account"
					click={resetMode}
					hideArrow={availableModes.length == 1}
				/>
			</span>
			<div
				class="mt-2 md:mt-0 px-6 py-6 md:w-full grid grid-cols gap-x-4 sm:gap-x-4 gap-y-2 text-sm rounded-lg bg-[#F4F6F9]"
				style="font-weight: 600;"
			>
				<div class="inline-flex pr-1.5 gap-x-2 text-sm">
					<svelte:component this={TPV_UNION} color="var(--text-secondary)" />
					<div class="value bank-name font-semibold">
						{subscription.payment_instrument_details.bank_name}
					</div>
				</div>
				<div class="label font-medium md:pt-3 account-number text-sm text-textSecondary">
					{$form.account_type === ACCOUNT_TYPES[1] ? 'Account Number' : 'Savings Account Number'}
				</div>
				<div class="value account-number font-semibold">
					{$form.account_number}
				</div>
				<div class="label font-medium md:pt-3 account-name text-sm text-textSecondary">
					{#if $form.account_type == ACCOUNT_TYPES[1]}
						Account Name
					{:else}
						Account Holder Name
					{/if}
				</div>
				<div class="value account-name font-semibold">
					{$form.account_holder_name}
				</div>
			</div>
			{#if $pageState.selectedBank.auth?.length > 1}
				<div class="pr-6 pb-4 account-authentication font-medium text-sm flex flex-wrap gap-4 pt-4">
					<div>Authenticate Account Using:</div>

					<div class="flex flex-col font-semibold px-2">
						{#each $pageState.selectedBank.auth as authType}
							<div class="form-check form-check-inline pb-2">
								<label class="flex font-medium items-center cursor-pointer">
									<input
										bind:group={$form.auth_mode}
										class="hidden"
										type="radio"
										name="auth_mode"
										id="auth_mode"
										value={authType}
										on:click={handleFormChange}
									/>
									<span
										class="inp-radio w-4 h-4 inline-block mr-2 rounded-full border-2 border-textSecondary"
									/>
									{ACCOUNT_AUTHENTICATION_TYPES_LABELS[authType]}
								</label>
							</div>
						{/each}
					</div>
				</div>
			{:else}
				<div class="account-authentication text-sm font-medium flex pt-4" style="font-weight: 600;">
					<span class="flex pr-2">
						<svelte:component this={ICON_INFORMATION} />
					</span>
					<span>
						Your account will be authenticated via<span class="pl-1.5 font-semibold text-[#D68309]"
							>{ACCOUNT_AUTHENTICATION_TYPES_LABELS[$pageState.selectedBank.auth[0]]}</span
						>
					</span>
				</div>
			{/if}
			<div class="authorization flex pt-4">
				<label class="form-check-label cursor-pointer relative">
					<input
						class="hidden"
						type="checkbox"
						checked={$form.is_authorized}
						id="is_authorized"
						name="is_authorized"
						on:change={handleFormChange}
					/>
					<span
						class="tick w-4 h-4 mb-4 inline-block absolute rounded-sm border-2 border-textSecondary"
					>
						<div class="flex justify-center h-full items-center">
							<svelte:component this={ICON_AUTHORIZE_TICK} />
						</div>
					</span>
					<div class="text-xs font-semibold ml-7" style="font-weight: 600;">
						I authorize {merchant.merchant_meta.merchant_name} to debit the amount mentioned above from
						my account as per the payment instructions stated.
					</div>
				</label>
			</div>
			<div class="flex pt-8">
				<button
					type="submit"
					disabled={!$isValid || !$pageState.formFilled}
					class={`grow inline-block h-10 px-6 py-2.5 font-semibold text-sm leading-tight rounded-md focus:outline-none focus:ring-0 transition duration-150 ease-in-out` +
						(!$isValid || !$pageState.formFilled || $isSubmitting
							? ` cursor-not-allowed bg-buttonDisabled text-textSecondary`
							: ` bg-productPrimary text-white`)}
				>
					{#if $isSubmitting}
						<span class="relative -top-0.5"><Loader enabledMode={!$isSubmitting} /></span>
					{:else}Next{/if}
				</button>
			</div>
		</div>
	{/if}
</form>
<PoweredBy selectedMode={PAYMENT_MODES[0]} />
