<script>
	import creditCardType from 'credit-card-type';

	// Components
	import PoweredBy from '$components/PoweredBy.svelte';

	// Helpers
	import { formatCardNumber, formatDate, formatExpiryDate } from '$utils/helpers';

	// Constants
	import { CURRENCY, PLAN_TYPES, DATE_FORMATS } from '$utils/constants';

	export let formCard;
	export let subscription;
	export let mode;
</script>

<div
	class="px-6 pb-4 font-medium md:w-full grid grid-cols-[10em_auto] gap-x-4 sm:gap-x-12 gap-y-4 text-sm"
>
	<!-- CARD HOLDER NAME -->
	<div class="label card-holder-name text-textSecondary">Card Holder Name</div>
	<div class="value card-holder-name">
		{formCard.card_holder_name}
	</div>
	<!-- CARD ENDING WITH -->
	<div class="label card-ending-with text-textSecondary">Card ending with</div>
	<div class="value card-ending-with">
		{formatCardNumber(formCard.card_number, creditCardType(formCard.card_number)[0], true)}
	</div>
	<!-- SUBSCRIPTION START DATE - END DATE -->
	<div class="label start-end-date text-textSecondary">Start & End Date</div>
	<div class="value start-end-date">
		{formatDate(subscription.start_date, DATE_FORMATS.DD_MMM_YYYY)} - {formatExpiryDate(
			subscription.end_date
		)}
	</div>
	<!-- SUBSCRIPTION NEXT CHARGE DATE -->
	{#if subscription.type == PLAN_TYPES[0] && !!subscription.calculated_first_charge_date && !!subscription.calculated_first_charge_date[mode]}
		<div class="label first-charge-date text-textSecondary">Next Charge Date</div>
		<div class="value first-charge-date">
			{formatDate(subscription.calculated_first_charge_date[mode], DATE_FORMATS.DD_MMM_YYYY)}
		</div>
	{/if}
	<!-- AUTHORIZATION AMOUNT -->
	{#if parseInt(subscription.authorization_amount, 10) <= 1}
		<div class="label auth-amount text-textSecondary">Authorization Amount</div>
		<div class="value auth-amount">
			{!!subscription.currency ? subscription.currency : subscription.currency}
			{subscription.authorization_amount}
		</div>
	{/if}
</div>

<PoweredBy selectedMode={mode} />
