<script>
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import { IS_PROD, IS_PROD_TEST, ENV_APP_BASE_PATH } from '$lib/Env';

	import { LottiePlayer } from '@lottiefiles/svelte-lottie-player';

	// State
	import { appState } from '$store/state/appState.svelte';
	import redirectAnimation from '$utils/redirectAnimation.json';

	// Constants
	import { PAYMENT_MODES, REDIRECTION_MODES } from '$utils/constants';
	import { UI_PATHS } from '$utils/path';
	import { stateSubscription } from '$store/state/subscription.svelte';
	import { statePaymentOptions } from '$store/state/paymentOptions.svelte';
	import { stateAmplitude } from '$store/state/amplitude.svelte';
	import { stateMerchant } from '$store/state/merchant.svelte';

	import {
		detectSdk,
		getAppListFromSDK,
		getIOSAppListFromSDK,
		isSupportedUPI,
		redirectionForAndroid,
		redirectionForIOS,
		removeKeyFromQueryParams
	} from '$utils/helpers';

	let state = $page.url.searchParams.get('state');
	let mode = $appState.selectedMode;
	let sessionToken = $appState.sessionToken;

	let formData;
	let action;
	let token;

	let subscription = $stateSubscription.data;
	let paymentOptions = $statePaymentOptions.data;
	let merchant = $stateMerchant.data;
	let amplitudeEvent = $stateAmplitude.data;
	let authID = $appState.mandateAuthorization?.authID;
	let upiUser = $appState.userUPI;
	let formUPI = $appState.formUPI;
	let data = {
		subscription: subscription,
		paymentOptions: {
			[PAYMENT_MODES[1]]: {
				available_handles: paymentOptions[PAYMENT_MODES[1]].available_handles.filter(
					(v) => v.handle === formUPI.upi_postfix
				)
			}
		},
		merchant: merchant,
		amplitudeEvent: amplitudeEvent,
		authID: authID,
		upiUser: upiUser,
		formUPI: formUPI
	};
	var onsdk = detectSdk('ios');

	switch (state) {
		case REDIRECTION_MODES.MANDATE_AUTHORIZATION:
			formData = $appState[state].formData;
			action =
				IS_PROD || IS_PROD_TEST
					? $appState[state].redirectURL
					: `${ENV_APP_BASE_PATH}${UI_PATHS.SIMULATOR}/${mode}`;
			switch (mode) {
				case PAYMENT_MODES[1]:
					formData.data = btoa(JSON.stringify(data));
					token = sessionToken;
					break;
				case PAYMENT_MODES[2]:
					token = IS_PROD || IS_PROD_TEST ? $appState[state].sbcData : sessionToken;
					break;
				default:
					token = sessionToken;
					break;
			}
			break;
		case REDIRECTION_MODES.MANDATE_AUTHORIZATION_CALLBACK:
			formData = $appState[state].data;
			action = $appState[state].returnURL;
			token = $appState[state].token;
			break;
		case REDIRECTION_MODES.MANDATE_REDIRECT:
			// TODO: add dismiss action for android as well
			// this will work only for ios
			if (onsdk) {
				window.webkit.messageHandlers.nativeProcess.postMessage('dismissWeb');
			}
			formData = $appState[state].formData;
			action = $appState[state].returnURL;
			break;
	}

	let selectedMode = $appState.selectedMode;
	let method = 'POST';

	let redirectForm;

	onMount(() => {
		redirectForm.submit();
	});
</script>

<div
	class="app-wrapper grid place-content-center grow relative bg-white md:bg-inherit md:bg-[url('/static/images/background.png')]"
>
	<div
		class="flex flex-col grow md:grow-0 big md:my-6 md:mx-auto overflow-hidden md:rounded-lg bg-white"
	>
		<span class="relative -left-4 md:left-0">
			<LottiePlayer
				src={redirectAnimation}
				autoplay={true}
				loop={true}
				renderer="svg"
				background="transparent"
				height={180}
				width={320}
			/>
		</span>
	</div>
</div>

<form bind:this={redirectForm} {action} {method}>
	{#if (selectedMode == PAYMENT_MODES[0] && !IS_PROD && state != REDIRECTION_MODES.MANDATE_REDIRECT) || selectedMode == PAYMENT_MODES[1] || selectedMode == PAYMENT_MODES[2]}
		<input type="hidden" name="hash" value={token} />
		<input type="hidden" name="mode" value={selectedMode} />
	{/if}
	{#each Object.entries(formData) as [formKey, formVal]}
		<input type="hidden" name={formKey} value={formVal} />
	{/each}
</form>
