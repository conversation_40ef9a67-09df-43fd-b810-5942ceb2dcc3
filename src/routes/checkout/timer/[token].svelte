<script context="module">
	export const load = async ({ params, fetch, session, stuff }) => {
		// Set session token
		let sessionToken = params.token;
		return {
			props: {
				sessionToken
			}
		};
	};
</script>

<script>
	import { onMount, onDestroy } from 'svelte';
	import { writable } from 'svelte/store';
	import { goto } from '$app/navigation';
	import { assets } from '$app/paths';

	// Components
	import CheckoutCardLayout from '$components/CheckoutCardLayout.svelte';
	import CardFooter from '$components/CardFooter.svelte';
	import TPV from '$components/TPV.svelte';

	// Constants
	import { UI_PATHS } from '$utils/path';
	import { ENV_APP_BASE_PATH } from '$lib/Env';

	// API
	import { getAuthStatus } from '.';

	// State
	import { appState } from '$store/state/appState.svelte';
	import { stateSubscription } from '$store/state/subscription.svelte';
	import { statePaymentOptions } from '$store/state/paymentOptions.svelte';
	import { stateMerchant } from '$store/state/merchant.svelte';
	import { stateAmplitude } from '$store/state/amplitude.svelte';

	// Actions
	import {
		updateCurrentPage,
		updateSessionToken,
		submitFormUPI,
		updateUPIUser
	} from '$store/actions/appState.svelte';

	// Utilities
	import { formatTime } from '$utils/helpers';
	import { AUTH_STATUS, PAYMENT_MODES, SBC_PAYMENT_MODE, PAGES } from '$utils/constants';
	import { track } from '$utils/amplitude';
	import { page } from '$app/stores';
	import { updateSubscriptionData } from '$store/actions/subscription.svelte';
	import { updatePaymentOptionsData } from '$store/actions/paymentOptions.svelte';
	import { updateMerchantData } from '$store/actions/merchant.svelte';
	import { updateAmplitude } from '$store/actions/amplitude.svelte';

	let UPI = `${assets}/logos/UPI.png`;
	let GreenTick = `${assets}/images/Green_Tick.svg`;
	let authID = $page.url.searchParams.get('auth_id');
	let data = JSON.parse(atob($page.url.searchParams.get('data')));

	export let sessionToken;

	let subscription;
	let paymentOptions;
	let merchant;
	let upiUser;
	let amplitudeEvent;
	let formUPI;

	const pageState = writable({
		isLoading: true,
		isErrored: false,
		// isActivated: false,
		// isOverlay: false,
		cardGrow: true,
		seconds: 5 * 60,
		// xRequestID: ''
		authStatus: AUTH_STATUS.CREATED
	});

	let cardGrow = $pageState.cardGrow;
	let timerSeconds = $pageState.seconds;
	let authStatus = $pageState.authStatus;

	let unsubscribePageState = pageState.subscribe((v) => {
		timerSeconds = v.seconds;
		authStatus = v.authStatus;
	});

	const unsubscribeStateSubscription = stateSubscription.subscribe((v) => {
		subscription = v.data;
	});
	const unsubscribeStatePaymentOptions = statePaymentOptions.subscribe((v) => {
		paymentOptions = v.data;
	});
	const unsubscribeStateMerchant = stateMerchant.subscribe((v) => {
		merchant = v.data;
	});
	const unsubscribeAppState = appState.subscribe((value) => {
		formUPI = value.formUPI;
		upiUser = value.userUPI;
	});
	const unsubscribeStateAmplitude = stateAmplitude.subscribe((v) => {
		amplitudeEvent = v.data;
	});
	onMount(() => {
		updateSubscriptionData(data.subscription);
		updatePaymentOptionsData(data.paymentOptions);
		updateMerchantData(data.merchant);
		submitFormUPI(data.formUPI);
		updateUPIUser(data.upiUser);
		updateSessionToken(sessionToken);
		updateAmplitude(sessionToken);
		if (!$appState.sessionToken) {
			pageState.update((s) => ({
				...s,
				isErrored: true,
				isLoading: false
			}));
		} else {
			pageState.update((s) => ({
				...s,
				isLoading: false
			}));
		}
		updateCurrentPage(PAGES.TIMER);
		startTimer();
	});

	onDestroy(unsubscribeStateSubscription);
	onDestroy(unsubscribeStateMerchant);
	onDestroy(unsubscribeStatePaymentOptions);
	onDestroy(unsubscribeAppState);
	onDestroy(unsubscribeStateAmplitude);
	onDestroy(unsubscribePageState);

	const startTimer = () => {
		let timerId, polling;
		// Set the target time for the countdown to end
		const endTime = new Date(Date.now() + (timerSeconds * 1000)); // 5 minutes from now

		// Function to update the timer
		const updateTimer = () => {
			const currentTime = new Date();
			const remainingTime = Math.round(endTime.getTime()) - Math.round(currentTime.getTime());

			// Check if the countdown has reached zero
			if (remainingTime <= 0) {
				clearInterval(timerId);
				clearInterval(polling);
				location.href = `${ENV_APP_BASE_PATH}${UI_PATHS.STATUS}/${sessionToken}`;
			} else {
				// Update the timer state here
				pageState.update((s) => ({
					...s,
					seconds: Math.round(remainingTime / 1000),
				}));

				// Schedule the next update
				timerId = setTimeout(updateTimer, 1000);
			}
		};

		updateTimer();

		// Polling every 5 seconds to check status
		polling = setInterval(async () => {
			if (authStatus != AUTH_STATUS.ACTIVE && authStatus != AUTH_STATUS.FAILED) {
				let { err, res } = await getAuthStatus(sessionToken, authID);
				updateAuthStatus({ err, res });
			} else {
				clearInterval(polling);
				location.href = `${ENV_APP_BASE_PATH}${UI_PATHS.STATUS}/${sessionToken}`;
			}
		}, 5000);
	};

	const updateAuthStatus = ({ err, res }) => {
		if (!err)
			pageState.update((s) => ({
				...s,
				authStatus: res.data.auth_status
			}));
	};

	const handleCancel = () => {};
	const cancelButtonText = 'Cancel payment';

	function popState(event) {
		// Cancel the event as stated by the standard.
		event.preventDefault();
		// track(
		// 	'click_timer_browser_back_button',
		// 	{
		// 		merchantType: merchant.merchant_meta.merchant_type,
		// 		linkCreationSource: subscription.source,
		// 		authID: '',
		// 		subscriptionMode: 'UPI',
		// 		upi_postfix: formUPI.upi_postfix,
		// 		upi_id_selection: formUPI.upi_id_selection,
		// 		paymentMode: SBC_PAYMENT_MODE.SBC_UPI,
		// 		verificationStatus: formUPI.verification_status
		// 	},
		// 	amplitudeEvent,
		// 	sessionToken
		// );
		// Chrome requires returnValue to be set.
		event.returnValue = '';
		return;
	}

	function beforeUnload(event) {
		// Cancel the event as stated by the standard.
		event.preventDefault();

		// TODO: Issue: Fails intermittently, causing a popup on Timer screen to leave / cancel
		// track(
		// 	'click_timer_browser_refresh_button',
		// 	{
		// 		merchantType: merchant.merchant_meta.merchant_type,
		// 		linkCreationSource: subscription.source,
		// 		authID: '',
		// 		subscriptionMode: 'UPI',
		// 		upi_postfix: formUPI.upi_postfix,
		// 		upi_id_selection: formUPI.upi_id_selection,
		// 		paymentMode: SBC_PAYMENT_MODE.SBC_UPI,
		// 		verificationStatus: formUPI.verification_status
		// 	},
		// 	amplitudeEvent,
		// 	sessionToken
		// );
		// Chrome requires returnValue to be set.
		event.returnValue = '';
		return;
	}
</script>

<svelte:window on:popstate={popState} on:beforeunload={beforeUnload} />

<CheckoutCardLayout
	{pageState}
	{merchant}
	grow={cardGrow}
	{subscription}
	selectedMode={PAYMENT_MODES[1]}
>
	<div class="flex flex-col grow justify-space-between pt-8">
		<div class="flex flex-col grow p-6 pt-8">
			<div class="text-textSecondary text-center">
				{#if subscription.tpv_enabled}
					<TPV tpv_data={subscription.payment_instrument_details} />
				{/if}
			</div>
			{#if !!formUPI.upi_id}
				<div class="text-textSecondary md:text-center">Mandate request sent to</div>
			{/if}
			<div class="pt-2 md:text-center " style="font-weight: 600;">
				{#if !!upiUser.name}
					<span class="verified-upi-name font-semibold">{upiUser.name}</span>
				{/if}
				{#if !!formUPI.upi_id}
					<span
						class={`verified-upi-number text-textSecondary ` +
							(!upiUser.name ? 'font-bold' : 'font-normal')}
					>
						({formUPI.upi_id})
					</span>
				{/if}
				{#if !!upiUser.name}
					<img class="!w-4 !m-0 inline-block" src={GreenTick} alt="Green Tick" />
				{/if}
			</div>
			<div class="pt-14 text-center">
				Open your {#if formUPI.upi_postfix && paymentOptions.UPI.available_handles[0]?.logo_url}
					<img
						class="upi inline"
						src={paymentOptions.UPI.available_handles[0]?.logo_url}
						alt="HANDLE_LOGO"
					/>
				{:else}
					<img class="upi inline w-8" src={UPI} alt="UPI" />
				{/if} app and complete the mandate registration within
			</div>
			<div class="timer pt-2 text-center">
				<span class="text-4xl font-medium text-textSuccess">{formatTime(timerSeconds)}</span>
				<span class="pl-2 text-lg">mins</span>
			</div>
		</div>
		<div>
			<div class="p-6 text-center">
				Do not press back or refresh button until the payment is complete
			</div>
			<!-- <CardFooter {handleCancel} {cancelButtonText} /> -->
		</div>
	</div>
</CheckoutCardLayout>

<style>
	img {
		/* Added to prevent firefox auto scaling svg */
		margin: 0 0.5rem;
		bottom: 1.5em;
		width: 2.5rem;
		height: 1em;
	}
</style>
