<script context="module">
	// API
	import { getSubscription, getPaymentOptions, getMerchant, checkBrowser, isMobileDeviceAndIosPlatform } from '.';

	export const load = async ({ params, fetch, session, stuff }) => {
		// TODO:
		// Set session token
		let token = params.token;

		if (!!token) {
			// Fetch Checkout Details
			let [resSubscription, resPaymentOptions, resMerchant] = await Promise.all([
				getSubscription(token),
				getPaymentOptions(token),
				getMerchant(token)
			]);
			return {
				props: {
					resSubscription,
					resPaymentOptions,
					resMerchant,
					token
				}
			};
		}
	};
</script>

<script>
	import { onMount, onDestroy } from 'svelte';
	import { writable } from 'svelte/store';
	import { goto } from '$app/navigation';
	import { track } from '$utils/amplitude';

	// Libraries
	import { addToast } from '$lib/Toast';

	// Components
	import Sidebar from '$components/Sidebar.svelte';
	import CheckoutCardLayout from '$components/CheckoutCardLayout.svelte';
	import Overlay from '$lib/Overlay.svelte';
	import FormEmandate from '$lib/forms/FormEmandate.svelte';
	import FormUPI from '$lib/forms/FormUPI.svelte';
	import FormCard from '$lib/forms/FormCard.svelte';
	import CancelRegistration from '$components/CancelRegistration.svelte';
	import NavigateBack from '$components/NavigateBack.svelte';
	import CardFooter from '$components/CardFooter.svelte';
	import PoweredBy from '$components/PoweredBy.svelte';

	// Confirm Components
	import ConfirmEmandate from '$lib/confirm/ConfirmEmandate.svelte';
	import ConfirmUPI from '$lib/confirm/ConfirmUPI.svelte';
	import ConfirmCard from '$lib/confirm/ConfirmCard.svelte';

	// Utils
	import {
		PAYMENT_MODES,
		PAGES,
		SBC_PAYMENT_MODE,
		UPI_FLOWS,
		UPI_AUTH_FLOW,
		DOWNTIME,
		OS
	} from '$utils/constants';

	// State
	import Amplitude, { stateAmplitude } from '$store/state/amplitude.svelte';
	import { appState } from '$store/state/appState.svelte';
	import { stateSubscription } from '$store/state/subscription.svelte';
	import { stateMerchant } from '$store/state/merchant.svelte';
	import { statePaymentOptions } from '$store/state/paymentOptions.svelte';

	// Actions
	import {
		switchMode,
		resetMode,
		updateCurrentPage,
		updateCurrentStep,
		submitFormEmandate,
		submitFormUPI,
		submitFormCard,
		updateVerifyUPI,
		updateSessionToken
	} from '$store/actions/appState.svelte';
	import { updateAmplitude } from '$store/actions/amplitude.svelte';
	import { updateSubscriptionResponse } from '$store/actions/subscription.svelte';
	import { updatePaymentOptionsResponse } from '$store/actions/paymentOptions.svelte';
	import { updateMerchantResponse } from '$store/actions/merchant.svelte';
	import { updateProcessingPartner } from '$store/actions/paymentOptions.svelte';
	import { reset, updateMandateAuthorization } from '$store/actions/appState.svelte';

	// API
	import { getProcessingPartner, createAuthorization, verifyAuthAttempt, verifyUPI, getCardTypeAndBankName } from '.';
	import { getAuthStatus } from '../timer';
	import DownTime from '$components/DownTime.svelte';
	import { getBowserData } from '$utils/helpers';

	export let resSubscription;
	export let resPaymentOptions;
	export let resMerchant;
	export let token;

	const pageState = writable({
		isModeUnavailable: false,
		isConfirmSubmitting: false,
		isLoading: true,
		isCancelled: false,
		isErrored: false,
		isActivated: false,
		isLinkExpired: false,
		isOverlay: false,
		cardGrow: true,
		xRequestID: '',
		os: '',
		onSdk: false,
	});

	// Constants
	const cancelButtonText = 'Cancel Registration';

	let selectedMode = $appState.selectedMode;
	let page = $appState.page;
	let step = $appState.currentStep;
	let formEmandate = $appState.formEmandate;
	let formUPI = $appState.formUPI;
	let formCard = $appState.formCard;
	let upiUser = $appState.userUPI;
	let mandateAuthData = $appState.mandateAuthorization;
	let subscription = $stateSubscription.data;
	let merchant = $stateMerchant.data;
	let paymentOptions = $statePaymentOptions.data;
	let isOverlay = $pageState.isOverlay;
	let cardGrow = $pageState.cardGrow;
	let isConfirmSubmitting = $pageState.isConfirmSubmitting;
	let availableModes = Object.entries(paymentOptions).filter((v) => !!v[1].isVisible);
	let amplitudeEvent = $stateAmplitude.data;

	const isModeUnavailable = () => {
		if (!!$statePaymentOptions.isPaymentOptionsFetched) {
			const enabledModes = Object.keys(paymentOptions).filter(
				(mode) => paymentOptions[mode].isVisible == true
			);

			// UPI Amount Check is present in Backend
			if (!enabledModes.length) {
				pageState.update((s) => ({
					...s,
					isModeUnavailable: true,
					cardGrow: false
				}));
			}
		}
	};

	const delay = (delayInMilliseconds) => new Promise((res) => setTimeout(res, delayInMilliseconds));

	const isPageErrored = () => {
		const xRequestID =
			$stateSubscription.subscriptionXRequestID ||
			$statePaymentOptions.paymentOptionsXRequestID ||
			$stateMerchant.merchantXRequestID;

		pageState.update((s) => ({
			...s,
			xRequestID: xRequestID,
			isErrored:
				$stateSubscription.isSubscriptionErrored ||
				$statePaymentOptions.isPaymentOptionsErrored ||
				$stateMerchant.isMerchantErrored
		}));
	};

	const unsubscribe = pageState.subscribe((v) => {
		cardGrow = v.cardGrow;
		isOverlay = v.isOverlay;
		isConfirmSubmitting = v.isConfirmSubmitting;
	});
	const unsubscribeStateSubscription = stateSubscription.subscribe((v) => {
		subscription = v.data;
		pageState.update((s) => ({
			...s,
			isActivated:
				v.data?.status === 'ACTIVE' || v.data?.status === 'BANK_APPROVAL_PENDING' || false,
			isCancelled: v.data.status === 'CANCELLED' || v.data.status === 'CUSTOMER_CANCELLED',
			isLinkExpired: v.data.status === 'LINK_EXPIRED',
		}));
		isPageErrored();
	});
	const unsubscribeStatePaymentOptions = statePaymentOptions.subscribe((v) => {
		paymentOptions = v.data;
		availableModes = Object.entries(paymentOptions).filter((v) => !!v[1].isVisible);
		pageState.update((s) => ({
			...s,
			isErrored: availableModes.length == 0
		}));
		isPageErrored();
		isModeUnavailable();
	});
	const unsubscribeStateMerchant = stateMerchant.subscribe((v) => {
		merchant = v.data;
		isPageErrored();
	});
	const unsubscribeAppState = appState.subscribe((value) => {
		selectedMode = value.selectedMode;
		page = value.page;
		step = value.currentStep;
		formEmandate = value.formEmandate;
		formUPI = value.formUPI;
		formCard = value.formCard;
		upiUser = value.userUPI;
		mandateAuthData = value.mandateAuthorization;
	});
	const unsubscribeStateAmplitude = stateAmplitude.subscribe((v) => {
		amplitudeEvent = v.data;
	});

	function EventCall() {
		track(
			'VIEW_CHECKOUT_PAGE',
			{
				linkCreationSource: $stateSubscription.data.source,
				merchantType: $stateMerchant.data.merchant_meta.merchant_type
			},
			$stateAmplitude.data,
			token
		);
	}

	onMount(() => {
		const { os } = getBowserData(window.navigator.userAgent);
		// Update data to state
		updateSubscriptionResponse(resSubscription);
		updatePaymentOptionsResponse(resPaymentOptions, {
			sub: resSubscription,
			os: os
		});
		updateMerchantResponse(resMerchant);
		// Update token to state
		updateSessionToken(token);
		updateAmplitude(token).then(() => {
			EventCall();
		});
		pageState.update((s) => ({
			...s,
			isLoading: false,
			os: os,
			onSdk: !!window.Android
		}));

		window.addEventListener('resize', handleResize);

		// Call handleResize once
		handleResize(null, true);

		if ($pageState.onSdk) {
			window.Android.merchantTheme(
				JSON.stringify({ theme_color: merchant.merchant_page_pref.theme_color })
			);
		}

		return () => window.removeEventListener('resize', handleResize);
	});

	onDestroy(unsubscribe);
	onDestroy(unsubscribeStateSubscription);
	onDestroy(unsubscribeStateMerchant);
	onDestroy(unsubscribeStatePaymentOptions);
	onDestroy(unsubscribeAppState);
	onDestroy(unsubscribeStateAmplitude);

	const handleEmandateSubmit = async (formData) => {
		submitFormEmandate(formData);
		// Fetch Checkout Processing Partner Details
		try {
			let resProcessingPartner = await getProcessingPartner(
				formData.account_type,
				selectedMode,
				token
			);
			updateProcessingPartner(selectedMode, resProcessingPartner);
			pageState.update((s) => ({
				...s,
				isLoading: false,
				cardGrow: false
			}));
			updateCurrentPage(PAGES.SUMMARY);
		} catch (err) {
			pageState.update((s) => ({
				...s,
				isErrored: true,
				isLoading: false
			}));
		}
	};

	const handleUPISubmit = (formData) => {
		submitFormUPI(formData);
		handleConfirm();
	};

	const handleCardSubmit = (formData) => {
		submitFormCard(formData);
		updateCurrentPage(PAGES.SUMMARY);
		pageState.update((s) => ({
			...s,
			cardGrow: false
		}));
	};

	const handleResize = (node, update) => {
		let width = node?.currentTarget.innerWidth || window.innerWidth;
		// md : tailwind min-width
		if (width > 768) {
			if ((!!update || selectedMode == null) && availableModes.length) {
				switchMode(availableModes[0][0]);
			}
		} else if (selectedMode == null && availableModes.length == 1) {
			switchMode(availableModes[0][0]);
		}
	};

	const handleCancel = () => {
		sendEvent('click_summary_cancel_button', {}, selectedMode);
		pageState.update((s) => ({
			...s,
			isOverlay: true
		}));
	};
	const handleModalCancel = () => {
		sendEvent('click_summary_cancel_no_button', {}, selectedMode);
		pageState.update((s) => ({
			...s,
			isOverlay: false
		}));
	};

	const handleConfirm = async () => {
		pageState.update((s) => ({ ...s, isConfirmSubmitting: true }));
		sendEvent('checkout_summary_confirm_submit', {}, selectedMode);
		let data;
		switch (selectedMode) {
			case PAYMENT_MODES[0]:
				if (!subscription.tpv_enabled) data = formEmandate;
				else {
					const { tpv_enabled, payment_instrument_details, ...form } = formEmandate;
					data = form;
				}
				break;
			case PAYMENT_MODES[1]:
				data = formUPI;
				data.upi_auth_flow = UPI_AUTH_FLOW[data.upi_auth_flow];
				break;
			case PAYMENT_MODES[2]:
				data = formCard;
				break;
		}

		createAuthorization(data, selectedMode, token).then(({ err, res }) => {
			pageState.update((s) => ({ ...s, isConfirmSubmitting: false }));
			const { os } = getBowserData(window.navigator.userAgent);
			updateMandateAuthorization({ err, res }, selectedMode);
			if (err) {
				addToast('Sorry it’s not you. It’s us!', 'warn', 3000);
			} else {
				if (
					selectedMode === PAYMENT_MODES[0] ||
					selectedMode === PAYMENT_MODES[2] ||
					(selectedMode === PAYMENT_MODES[1] &&
						data.upi_auth_flow === UPI_AUTH_FLOW.COLLECT)
				) {
					goto('/redirect?state=mandateAuthorization');
				} else if (
					selectedMode === PAYMENT_MODES[1] && 
					data.upi_auth_flow === UPI_AUTH_FLOW.INTENT
				) {	
					if ($pageState.os === OS.ANDROID) {
						goto('/redirect?state=mandateAuthorization');
					} else if (isMobileDeviceAndIosPlatform()) {
						if (!checkBrowser()) {
							goto('/redirect?state=mandateAuthorization');
						} else {
							delay(7000).then(() => {
								goto('/redirect?state=mandateAuthorization');
							});
						}
					}
				} else {
					// DO NOTHING
				}
			}
		});
	};

	const sendEvent = (event, eventProperties, selectedMode) => {
		track(
			event,
			{
				linkCreationSource: subscription.source,
				merchantType: merchant.merchant_meta.merchant_type,
				authID: '',
				...(selectedMode === PAYMENT_MODES[0]
					? {
							accountType: formEmandate.account_type,
							bank: formEmandate.bank_id,
							paymentMode: SBC_PAYMENT_MODE.SBC_NPCI,
							subscriptionMode: 'BANK ACCOUNT',
							authorizationMode: formEmandate.auth_mode
					  }
					: selectedMode == PAYMENT_MODES[1]
					? {
							subscriptionMode: selectedMode,
							upi_postfix: formUPI.upi_postfix,
							upi_id_selection: formUPI.upi_id_selection,
							paymentMode: SBC_PAYMENT_MODE.SBC_UPI,
							verificationStatus: formUPI.verification_status
					  }
					: {
							linkCreationSource: subscription.source,
							merchantType: merchant.merchant_meta.merchant_type,
							paymentMode: SBC_PAYMENT_MODE.SBC_CARD,
							subscriptionMode: selectedMode,
							verificationStatus: formCard.verification_status,
							cardNetwork: formCard.card_network
					  })
			},
			amplitudeEvent,
			token
		);
	};
</script>

<svelte:head>
	<title>Checkout</title>
</svelte:head>

<CheckoutCardLayout
	{pageState}
	{merchant}
	grow={cardGrow}
	{subscription}
	{selectedMode}
	{amplitudeEvent}
	{token}
>
	{#if page == PAGES.FORM}
		<div class="flex grow">
			<Sidebar
				{paymentOptions}
				{selectedMode}
				switchMode={(mode) => {
					track(
						'click_subscription_mode',
						{
							subscriptionMode: mode == 'E_MANDATE' ? 'BANK ACCOUNT' : mode,
							linkCreationSource: subscription.source,
							merchantType: merchant.merchant_meta.merchant_type
						},
						amplitudeEvent,
						token
					);
					switchMode(mode);
				}}
			/>
			<div class="content flex flex-col grow justify-between px-6 pt-6">
				{#if selectedMode === PAYMENT_MODES[0] && !DOWNTIME.includes(selectedMode)}
					<FormEmandate
						{amplitudeEvent}
						{paymentOptions}
						{merchant}
						{subscription}
						formState={$appState.formEmandate}
						{handleEmandateSubmit}
						{step}
						{updateCurrentStep}
						{resetMode}
						{availableModes}
						{token}
						{verifyAuthAttempt}
					/>
				{:else if selectedMode == PAYMENT_MODES[1] && !DOWNTIME.includes(selectedMode)}
					<FormUPI
						{token}
						{subscription}
						{paymentOptions}
						{merchant}
						formState={$appState.formUPI}
						{verifyUPI}
						{updateVerifyUPI}
						{mandateAuthData}
						{handleUPISubmit}
						{resetMode}
						{availableModes}
						{amplitudeEvent}
						{getAuthStatus}
						{isConfirmSubmitting}
						os={$pageState.os}
					/>
				{:else if selectedMode === PAYMENT_MODES[2] && !DOWNTIME.includes(selectedMode)}
					<FormCard
						{amplitudeEvent}
						{token}
						{subscription}
						{paymentOptions}
						{merchant}
						formState={$appState.formCard}
						{verifyAuthAttempt}
						{handleCardSubmit}
						{resetMode}
						{availableModes}
						{getCardTypeAndBankName}
					/>
				{:else}
					<!-- Future Scope: Need to show DOWNTIME timing from api -->
					<DownTime />
					<!-- Show Nothing -->
					<!-- No selected mode of payment -->
				{/if}
			</div>
		</div>
	{:else if page == PAGES.SUMMARY}
		<div class="flex flex-col justify-between content grow pt-4">
			<div class="min-h-[50vh] max-h-[60vh] overflow-y-scroll flex flex-col">
				<div class="px-6 pt-2">
					<NavigateBack
						click={() => {
							sendEvent('click_summary_back_button', {}, selectedMode);
							pageState.update((s) => ({
								...s,
								cardGrow: true
							}));
							updateCurrentPage(PAGES.FORM);
						}}
					/>
				</div>
				<div class="px-6 pt-2 pb-4 font-semibold">Summary</div>
				<div class="py-2 grow flex flex-col justify-between">
					{#if selectedMode === PAYMENT_MODES[0]}
						<ConfirmEmandate {formEmandate} {subscription} mode={selectedMode} />
					{:else if selectedMode == PAYMENT_MODES[1]}
						<ConfirmUPI {formUPI} {subscription} mode={selectedMode} {upiUser} />
					{:else if selectedMode === PAYMENT_MODES[2]}
						<ConfirmCard {formCard} {subscription} mode={selectedMode} />
					{:else}
						<!-- Show Nothing -->
						No available mode of payment
					{/if}
				</div>
			</div>
			<div>
				<CardFooter
					{handleConfirm}
					{handleCancel}
					{cancelButtonText}
					isConfirmSubmitting={$pageState.isConfirmSubmitting}
				/>
			</div>
			<span class="md:hidden">
				<PoweredBy {selectedMode} />
			</span>
		</div>
	{/if}
</CheckoutCardLayout>
<Overlay {isOverlay}>
	<CancelRegistration
		{selectedMode}
		handleReject={handleModalCancel}
		handleConfirm={() => {
			sendEvent('click_summary_cancel_yes_button', {}, selectedMode);
			// reset(); // This causes value to show undefined in Modal
			window.location.reload();
		}}
	/>
</Overlay>
