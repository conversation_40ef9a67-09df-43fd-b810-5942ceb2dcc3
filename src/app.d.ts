// See https://kit.svelte.dev/docs/types#app
// for information about these interfaces
declare global {
    interface Window {
        Android: any;
        webkit: any;
        receiveAppList: any;
        clarity: any;
        showVerifyUI: any;
        verifyPaymentForiOS: any;
        PaymentJSInterface: any;
        AndroidOTPInterface: any;
        clevertap: any;
        fbq: any;
        setOTP: any;
    }

    namespace App {
        // interface Error {}
        // interface Locals {}
        // interface PageData {}
        // interface Platform {}
    }
}

export {};
