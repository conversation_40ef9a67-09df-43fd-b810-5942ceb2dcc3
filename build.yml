image:
  name: '185152735195.dkr.ecr.ap-south-1.amazonaws.com/build_tools:node-20-alpine'
  aws:
    access-key: $AWS_ACCESS_KEY_ID
    secret-key: $AWS_SECRET_ACCESS_KEY
build_step:
  name: Upload Docker Image to AWS ECR
  image:
    name: '185152735195.dkr.ecr.ap-south-1.amazonaws.com/build_tools:node-20-alpine'
    aws:
      access-key: $AWS_ACCESS_KEY_ID
      secret-key: $AWS_SECRET_ACCESS_KEY
  runs-on:
    - 'self.hosted'
    - 'nonprod.runner'
  script:
    - source ENV_VARIABLES
    - source TAGS
    - if [[ $source_code_changed == true ]]; then
    # Build Docker Image
    - sh scripts/build_docker.sh $app_name prod $image_tag
    - docker images -a
    # Push Docker Image to AWS ECR
    - pipe: atlassian/aws-ecr-push-image:2.0.0
      variables:
        IMAGE_NAME: $app_name
        TAGS: $image_tag
    - else
    - echo "Source code files unchanged, skipping image building step!!"
    - fi
    # Dummy comment to trigger pipeline
overrides:
  pipelines:
    - 'feature':
        # - qa
        # - sbox
        # - prodint
        - prod
    - 'produae':    
        - produae
  sonar_language: Npm
  sonar_enabled: false
  sonar_quality_gate_enabled: false
  integration_test_enabled: false
