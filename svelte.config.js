import adapter from '@sveltejs/adapter-node';
import preprocess from 'svelte-preprocess';
import { resolve } from 'path';

// TODO: Temporary Workaround, remove once sveltekit is updated.
let BASE_PATH = '';
// switch (process.env.VITE_APP_ENV) {
// 	case 'dev':
// 	case 'prod':
// 		BASE_PATH = '';
// 		break;
// 	case 'beta':
// 	case 'test':
// 	case 'gamma':
// 	case 'prod-test':
// 		BASE_PATH = '/subs-checkout';
// 		break;
// }

/** @type {import('@sveltejs/kit').Config} */
const config = {
	// Consult https://github.com/sveltejs/svelte-preprocess
	// for more information about preprocessors
	preprocess: preprocess(),

	kit: {
		adapter: adapter({
			precompress: true
		}),
		paths: {
			base: BASE_PATH,
			assets: process.env.VITE_APP_ASSET_PATH
		},
		alias: {
			$components: resolve('./src/components'),
			$containers: resolve('./src/containers'),
			$store: resolve('./src/store/'),
			$api: resolve('./src/routes/api'),
			$utils: resolve('./src/utilities')
		},
		version: {
			name: Date.now().toString(),
			pollInterval: 0
		}
	}
};

export default config;
