import stoppable from 'stoppable';
import process from 'process';

import logger from './logger.js';

const servers = [];

function shutdown(server, timeout) {
    servers.push(server.server);
    stoppable(server.server, timeout);
}

// handle shutdown
function handleShutdown(signal) {
    const closers = servers.map((server) => {
        const port = server.address().port;
        logger.info(`${signal} signal received: closing HTTP server at port ${port}`);
        return new Promise((resolve, reject) => {
            server.stop((err) => {
                logger.info(`HTTP server at port ${port} closed`);
                if (err) reject(err);
                else resolve();
            });
        });
    });
    return Promise.all(closers)
        .then(() => process.exit(0))
        .catch(() => process.exit(1));
}

process.on('SIGINT', handleShutdown);
process.on('SIGTERM', handleShutdown);

export default shutdown;
