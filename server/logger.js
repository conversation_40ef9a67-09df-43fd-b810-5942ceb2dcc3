import pino from 'pino';
import rTracer from 'cls-rtracer';

export default pino({
    mixin() {
        return {
            'x-request-id': rTracer.id()
        };
    },
    formatters: {
        level(level) {
            return {
                level: level.toUpperCase()
            };
        }
    },
    level: process.env.LOG_LEVEL || 'info',
    messageKey: 'message',
    timestamp: () => `,"@timestamp":"${Date.now()}"`
});
